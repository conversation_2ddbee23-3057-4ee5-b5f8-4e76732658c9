{"hash": "54e92bd2", "configHash": "fceeacfc", "lockfileHash": "be5e630b", "browserHash": "912063a4", "optimized": {"@git-diff-view/file": {"src": "../../@git-diff-view/file/dist/esm/index.mjs", "file": "@git-diff-view_file.js", "fileHash": "65a657c5", "needsInterop": false}, "@git-diff-view/vue": {"src": "../../@git-diff-view/vue/dist/vue-git-diff-view.mjs", "file": "@git-diff-view_vue.js", "fileHash": "3e086720", "needsInterop": false}, "@tauri-apps/api/core": {"src": "../../@tauri-apps/api/core.js", "file": "@tauri-apps_api_core.js", "fileHash": "5ba0d69b", "needsInterop": false}, "@tauri-apps/api/webviewWindow": {"src": "../../@tauri-apps/api/webviewWindow.js", "file": "@tauri-apps_api_webviewWindow.js", "fileHash": "ec169ede", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "0f598b6e", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "b612f8b1", "needsInterop": false}}, "chunks": {"chunk-KL37IYN5": {"file": "chunk-KL37IYN5.js"}, "chunk-CJY6KJOW": {"file": "chunk-CJY6KJOW.js"}, "chunk-ZY5X6FX7": {"file": "chunk-ZY5X6FX7.js"}, "chunk-EQCVQC35": {"file": "chunk-EQCVQC35.js"}}}