{"version": 3, "sources": ["../../@git-diff-view/utils/dist/esm/index.mjs", "../../@git-diff-view/vue/src/context/provider.ts", "../../@git-diff-view/vue/src/context/inject.ts", "../../@git-diff-view/vue/src/hooks/useIsMounted.ts", "../../@git-diff-view/vue/src/hooks/useProvide.ts", "../../@git-diff-view/vue/src/hooks/useSubscribeDiffFile.ts", "../../@git-diff-view/vue/src/hooks/useTextWidth.ts", "../../@git-diff-view/vue/src/components/DiffAddWidget.tsx", "../../@git-diff-view/vue/src/components/DiffNoNewLine.tsx", "../../@git-diff-view/vue/src/components/DiffContent.tsx", "../../@git-diff-view/vue/src/components/DiffSplitContentLineNormal.tsx", "../../@git-diff-view/vue/src/hooks/useDomWidth.ts", "../../@git-diff-view/vue/src/hooks/useSyncHeight.ts", "../../@git-diff-view/vue/src/components/DiffSplitExtendLineNormal.tsx", "../../@git-diff-view/vue/src/components/DiffExpand.tsx", "../../@git-diff-view/vue/src/components/DiffSplitHunkLineNormal.tsx", "../../@git-diff-view/vue/src/components/DiffSplitWidgetLineNormal.tsx", "../../@git-diff-view/vue/src/components/DiffSplitViewNormal.tsx", "../../@git-diff-view/vue/src/components/DiffSplitContentLineWrap.tsx", "../../@git-diff-view/vue/src/components/DiffSplitExtendLineWrap.tsx", "../../@git-diff-view/vue/src/components/DiffSplitHunkLineWrap.tsx", "../../@git-diff-view/vue/src/components/DiffSplitWidgetLineWrap.tsx", "../../@git-diff-view/vue/src/components/DiffSplitViewWrap.tsx", "../../@git-diff-view/vue/src/components/DiffSplitView.tsx", "../../@git-diff-view/vue/src/components/DiffUnifiedContentLine.tsx", "../../@git-diff-view/vue/src/components/DiffUnifiedExtendLine.tsx", "../../@git-diff-view/vue/src/components/DiffUnifiedHunkLine.tsx", "../../@git-diff-view/vue/src/components/DiffUnifiedWidgetLine.tsx", "../../@git-diff-view/vue/src/components/DiffUnifiedView.tsx", "../../@git-diff-view/vue/src/components/DiffView.tsx"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar _TextMeasure_instances, _TextMeasure_key, _TextMeasure_map, _TextMeasure_getInstance;\nlet canvasCtx = null;\nconst getKey = (font, text) => {\n    return `${font.fontFamily}-${font.fontStyle}-${font.fontSize}-${text}`;\n};\nconst getStableKey = (font, text) => {\n    return getKey(font, \"0\".repeat(text.length));\n};\nclass TextMeasure {\n    constructor() {\n        _TextMeasure_instances.add(this);\n        _TextMeasure_key.set(this, \"\");\n        _TextMeasure_map.set(this, {});\n    }\n    measure(text, font) {\n        const currentKey = getStableKey(font, text);\n        if (__classPrivateFieldGet(this, _TextMeasure_map, \"f\")[currentKey]) {\n            return __classPrivateFieldGet(this, _TextMeasure_map, \"f\")[currentKey];\n        }\n        const instance = __classPrivateFieldGet(this, _TextMeasure_instances, \"m\", _TextMeasure_getInstance).call(this);\n        if (font) {\n            const currentFontKey = `${font.fontFamily}-${font.fontStyle}-${font.fontSize}`;\n            if (__classPrivateFieldGet(this, _TextMeasure_key, \"f\") !== currentFontKey) {\n                __classPrivateFieldSet(this, _TextMeasure_key, currentFontKey, \"f\");\n                instance.font = `${font.fontStyle || \"\"} ${font.fontSize || \"\"} ${font.fontFamily || \"\"}`;\n            }\n        }\n        else {\n            instance.font = \"\";\n        }\n        const textWidth = instance.measureText(text).width;\n        return textWidth;\n    }\n}\n_TextMeasure_key = new WeakMap(), _TextMeasure_map = new WeakMap(), _TextMeasure_instances = new WeakSet(), _TextMeasure_getInstance = function _TextMeasure_getInstance() {\n    canvasCtx = canvasCtx || document.createElement(\"canvas\").getContext(\"2d\");\n    return canvasCtx;\n};\nlet instance = null;\nconst getTextMeasureInstance = () => {\n    instance = instance || new TextMeasure();\n    return instance;\n};\n\nconst addContentBGName = \"--diff-add-content--\";\nconst delContentBGName = \"--diff-del-content--\";\nconst borderColorName = \"--diff-border--\";\nconst addLineNumberBGName = \"--diff-add-lineNumber--\";\nconst delLineNumberBGName = \"--diff-del-lineNumber--\";\nconst plainContentBGName = \"--diff-plain-content--\";\nconst expandContentBGName = \"--diff-expand-content--\";\nconst plainLineNumberColorName = \"--diff-plain-lineNumber-color--\";\nconst expandLineNumberColorName = \"--diff-expand-lineNumber-color--\";\nconst plainLineNumberBGName = \"--diff-plain-lineNumber--\";\nconst expandLineNumberBGName = \"--diff-expand-lineNumber--\";\nconst hunkContentBGName = \"--diff-hunk-content--\";\nconst hunkContentColorName = \"--diff-hunk-content-color--\";\nconst hunkLineNumberBGName = \"--diff-hunk-lineNumber--\";\nconst hunkLineNumberBGHoverName = \"--diff-hunk-lineNumber-hover--\";\nconst addContentHighlightBGName = \"--diff-add-content-highlight--\";\nconst delContentHighlightBGName = \"--diff-del-content-highlight--\";\nconst addWidgetBGName = \"--diff-add-widget--\";\nconst addWidgetColorName = \"--diff-add-widget-color--\";\nconst emptyBGName = \"--diff-empty-content--\";\nconst getContentBG = (isAdded, isDelete, hasDiff) => {\n    return isAdded\n        ? `var(${addContentBGName})`\n        : isDelete\n            ? `var(${delContentBGName})`\n            : hasDiff\n                ? `var(${plainContentBGName})`\n                : `var(${expandContentBGName})`;\n};\nconst getLineNumberBG = (isAdded, isDelete, hasDiff) => {\n    return isAdded\n        ? `var(${addLineNumberBGName})`\n        : isDelete\n            ? `var(${delLineNumberBGName})`\n            : hasDiff\n                ? `var(${plainLineNumberBGName})`\n                : `var(${expandLineNumberBGName})`;\n};\n\nconst removeAllSelection = () => {\n    const selection = window.getSelection();\n    for (let i = 0; i < selection.rangeCount; i++) {\n        selection.removeRange(selection.getRangeAt(i));\n    }\n};\nconst syncScroll = (left, right) => {\n    const onScroll = function (event) {\n        if (event === null || event.target === null)\n            return;\n        if (event.target === left) {\n            right.scrollTop = left.scrollTop;\n            right.scrollLeft = left.scrollLeft;\n        }\n        else {\n            left.scrollTop = right.scrollTop;\n            left.scrollLeft = right.scrollLeft;\n        }\n    };\n    if (!left.onscroll) {\n        left.onscroll = onScroll;\n    }\n    if (!right.onscroll) {\n        right.onscroll = onScroll;\n    }\n    return () => {\n        left.onscroll = null;\n        right.onscroll = null;\n    };\n};\n\nconst diffFontSizeName = \"--diff-font-size--\";\nconst diffAsideWidthName = \"--diff-aside-width--\";\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nconst memoFunc = (func) => {\n    const cache = {};\n    return ((key) => {\n        if (cache[key]) {\n            return cache[key];\n        }\n        const result = func(key);\n        cache[key] = result;\n        return result;\n    });\n};\n\nvar NewLineSymbol;\n(function (NewLineSymbol) {\n    NewLineSymbol[NewLineSymbol[\"CRLF\"] = 1] = \"CRLF\";\n    NewLineSymbol[NewLineSymbol[\"CR\"] = 2] = \"CR\";\n    NewLineSymbol[NewLineSymbol[\"LF\"] = 3] = \"LF\";\n    NewLineSymbol[NewLineSymbol[\"NEWLINE\"] = 4] = \"NEWLINE\";\n    NewLineSymbol[NewLineSymbol[\"NORMAL\"] = 5] = \"NORMAL\";\n    NewLineSymbol[NewLineSymbol[\"NULL\"] = 6] = \"NULL\";\n})(NewLineSymbol || (NewLineSymbol = {}));\nconst getSymbol = (symbol) => {\n    switch (symbol) {\n        case NewLineSymbol.LF:\n            return \"␊\";\n        case NewLineSymbol.CR:\n            return \"␍\";\n        case NewLineSymbol.CRLF:\n            return \"␍␊\";\n        default:\n            return \"\";\n    }\n};\nvar DiffModeEnum;\n(function (DiffModeEnum) {\n    // github like\n    DiffModeEnum[DiffModeEnum[\"SplitGitHub\"] = 1] = \"SplitGitHub\";\n    // gitlab like\n    DiffModeEnum[DiffModeEnum[\"SplitGitLab\"] = 2] = \"SplitGitLab\";\n    DiffModeEnum[DiffModeEnum[\"Split\"] = 3] = \"Split\";\n    DiffModeEnum[DiffModeEnum[\"Unified\"] = 4] = \"Unified\";\n})(DiffModeEnum || (DiffModeEnum = {}));\n\nexport { DiffModeEnum, NewLineSymbol, addContentBGName, addContentHighlightBGName, addLineNumberBGName, addWidgetBGName, addWidgetColorName, borderColorName, delContentBGName, delContentHighlightBGName, delLineNumberBGName, diffAsideWidthName, diffFontSizeName, emptyBGName, expandContentBGName, expandLineNumberBGName, expandLineNumberColorName, getContentBG, getLineNumberBG, getSymbol, getTextMeasureInstance, hunkContentBGName, hunkContentColorName, hunkLineNumberBGHoverName, hunkLineNumberBGName, memoFunc, plainContentBGName, plainLineNumberBGName, plainLineNumberColorName, removeAllSelection, syncScroll };\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n", "import type { Diff<PERSON><PERSON><PERSON><PERSON>, SplitSide } from \"..\";\nimport type { DiffFile } from \"@git-diff-view/core\";\nimport type { InjectionKey, Ref, Slot } from \"vue\";\n\nexport const idSymbol: InjectionKey<Ref<string>> = Symbol();\n\nexport const mountedSymbol: InjectionKey<Ref<boolean>> = Symbol();\n\nexport const modeSymbol: InjectionKey<Ref<DiffModeEnum>> = Symbol();\n\nexport const fontSizeSymbol: InjectionKey<Ref<number>> = Symbol();\n\nexport const enableWrapSymbol: InjectionKey<Ref<boolean>> = Symbol();\n\nexport const enableHighlightSymbol: InjectionKey<Ref<boolean>> = Symbol();\n\nexport const enableAddWidgetSymbol: InjectionKey<Ref<boolean>> = Symbol();\n\nexport const slotsSymbol: InjectionKey<{\n  widget: Slot<{ lineNumber: number; side: SplitSide; diffFile: DiffFile; onClose: () => void }>;\n  extend: Slot<{ lineNumber: number; side: SplitSide; data: any; diffFile: DiffFile; onUpdate: () => void }>;\n}> = Symbol();\n\nexport const extendDataSymbol: InjectionKey<\n  Ref<{ oldFile?: Record<string, { data: any }>; newFile?: Record<string, { data: any }> }>\n> = Symbol();\n\nexport const onAddWidgetClickSymbol: InjectionKey<\n  (event: \"onAddWidgetClick\", lineNumber: number, side: SplitSide) => void\n> = Symbol();\n\nexport const widgetStateSymbol: InjectionKey<Ref<{ lineNumber?: number; side?: SplitSide }>> = Symbol();\n\nexport const setWidgetStateSymbol: InjectionKey<(props: { lineNumber?: number; side?: SplitSide }) => void> = Symbol();\n", "import { inject } from \"vue\";\n\nimport {\n  enableAddWidgetSymbol,\n  enableHighlightSymbol,\n  enableWrapSymbol,\n  extendDataSymbol,\n  fontSizeSymbol,\n  idSymbol,\n  modeSymbol,\n  mountedSymbol,\n  onAddWidgetClickSymbol,\n  setWidgetStateSymbol,\n  slotsSymbol,\n  widgetStateSymbol,\n} from \"./provider\";\n\nexport const useId = () => inject(idSymbol);\n\nexport const useMode = () => inject(modeSymbol);\n\nexport const useIsMounted = () => inject(mountedSymbol);\n\nexport const useFontSize = () => inject(fontSizeSymbol);\n\nexport const useEnableWrap = () => inject(enableWrapSymbol);\n\nexport const useEnableHighlight = () => inject(enableHighlightSymbol);\n\nexport const useEnableAddWidget = () => inject(enableAddWidgetSymbol);\n\nexport const useExtendData = () => inject(extendDataSymbol);\n\nexport const useOnAddWidgetClick = () => inject(onAddWidgetClickSymbol);\n\nexport const useSlots = () => inject(slotsSymbol);\n\nexport const useWidget = () => inject(widgetStateSymbol);\n\nexport const useSetWidget = () => inject(setWidgetStateSymbol);\n", "import { onMounted, ref } from \"vue\";\n\nexport const useIsMounted = () => {\n  const isMount = ref(false);\n\n  onMounted(() => {\n    isMount.value = true;\n  });\n\n  return isMount;\n};\n", "import { provide, ref, watch } from \"vue\";\n\nimport type { InjectionKey } from \"vue\";\n\nexport const useProvide = <T extends Record<string, any>, K extends keyof T = keyof T>(\n  props: T,\n  key: K,\n  keySymbol: InjectionKey<any>,\n  option?: { defaultValue?: T[K]; deepWatch?: boolean }\n) => {\n  const value = ref(props?.[key] || option?.defaultValue);\n\n  watch(\n    () => props?.[key],\n    () => {\n      value.value = props[key];\n    },\n    { deep: option?.deepWatch }\n  );\n\n  provide(keySymbol, value);\n};\n", "import { watchEffect } from \"vue\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const useSubscribeDiffFile = (props: { diffFile: DiffFile }, onUpdate: (instance: DiffFile) => void) => {\n  const initSubscribe = (onClean: (cb: () => void) => void) => {\n    const diffFile = props.diffFile;\n\n    onUpdate(diffFile);\n\n    const clean = diffFile.subscribe(() => onUpdate(diffFile));\n\n    onClean(clean);\n  };\n\n  watchEffect(initSubscribe);\n};\n", "import { getTextMeasureInstance } from \"@git-diff-view/utils\";\nimport { ref, watchPostEffect } from \"vue\";\n\nimport { useIsMounted } from \"./useIsMounted\";\n\nimport type { Ref } from \"vue\";\n\nexport const useTextWidth = ({\n  text,\n  font,\n}: {\n  text: Ref<string>;\n  font: Ref<{ fontFamily?: string; fontStyle?: string; fontSize?: string }>;\n}) => {\n  const isMounted = useIsMounted();\n\n  const fontSize = parseInt(font.value.fontSize);\n\n  let baseSize = 6;\n\n  baseSize += fontSize > 10 ? (fontSize - 10) * 0.6 : 0;\n\n  const width = ref(baseSize * text.value.length);\n\n  const measureText = () => {\n    if (!isMounted.value) return;\n    width.value = getTextMeasureInstance().measure(text.value || \"\", font.value);\n  };\n\n  watchPostEffect(measureText);\n\n  return width;\n};\n", "import { addWidgetBGName, addWidgetColorName, diffFontSizeName } from \"@git-diff-view/utils\";\n\nimport { type SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitAddWidget = ({\n  side,\n  className,\n  lineNumber,\n  onWidgetClick,\n  onOpenAddWidget,\n}: {\n  index: number;\n  className?: string;\n  lineNumber: number;\n  diffFile: DiffFile;\n  side: SplitSide;\n  onOpenAddWidget: (lineNumber: number, side: SplitSide) => void;\n  onWidgetClick?: (event: \"onAddWidgetClick\", lineNumber: number, side: SplitSide) => void;\n}) => {\n  return (\n    <div\n      class={\n        \"diff-add-widget-wrapper invisible select-none transition-transform hover:scale-110 group-hover:visible\" +\n        (className ? \" \" + className : \"\")\n      }\n      style={{\n        width: `calc(var(${diffFontSizeName}) * 1.4)`,\n        height: `calc(var(${diffFontSizeName}) * 1.4)`,\n      }}\n    >\n      <button\n        class=\"diff-add-widget z-[1] flex h-full w-full origin-center cursor-pointer items-center justify-center rounded-md text-[1.2em]\"\n        style={{\n          color: `var(${addWidgetColorName})`,\n          backgroundColor: `var(${addWidgetBGName})`,\n        }}\n        onClick={() => {\n          onOpenAddWidget(lineNumber, side);\n          onWidgetClick?.(\"onAddWidgetClick\", lineNumber, side);\n        }}\n      >\n        +\n      </button>\n    </div>\n  );\n};\n\nexport const DiffUnifiedAddWidget = ({\n  lineNumber,\n  side,\n  onWidgetClick,\n  onOpenAddWidget,\n}: {\n  index: number;\n  diffFile: DiffFile;\n  lineNumber: number;\n  side: SplitSide;\n  onOpenAddWidget: (lineNumber: number, side: SplitSide) => void;\n  onWidgetClick?: (event: \"onAddWidgetClick\", lineNumber: number, side: SplitSide) => void;\n}) => {\n  return (\n    <div\n      class=\"diff-add-widget-wrapper invisible absolute left-[100%] top-[1px] translate-x-[-50%] select-none transition-transform hover:scale-110 group-hover:visible\"\n      style={{\n        width: `calc(var(${diffFontSizeName}) * 1.4)`,\n        height: `calc(var(${diffFontSizeName}) * 1.4)`,\n      }}\n    >\n      <button\n        class=\"diff-add-widget z-[1] flex h-full w-full origin-center cursor-pointer items-center justify-center rounded-md text-[1.2em]\"\n        style={{\n          color: `var(${addWidgetColorName})`,\n          backgroundColor: `var(${addWidgetBGName})`,\n        }}\n        onClick={() => {\n          onOpenAddWidget(lineNumber, side);\n          onWidgetClick?.(\"onAddWidgetClick\", lineNumber, side);\n        }}\n      >\n        +\n      </button>\n    </div>\n  );\n};\n", "export const DiffNoNewLine = () => {\n  return (\n    <svg aria-label=\"No newline at end of file\" role=\"img\" viewBox=\"0 0 16 16\" version=\"1.1\" fill=\"currentColor\">\n      <path d=\"M4.25 7.25a.75.75 0 0 0 0 1.5h7.5a.75.75 0 0 0 0-1.5h-7.5Z\"></path>\n      <path d=\"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0Zm-1.5 0a6.5 6.5 0 1 0-13 0 6.5 6.5 0 0 0 13 0Z\"></path>\n    </svg>\n  );\n};\n", "import {\n  DiffLineType,\n  getPlainDiffTemplate,\n  getPlainLineTemplate,\n  getSyntaxDiffTemplate,\n  getSyntaxLineTemplate,\n} from \"@git-diff-view/core\";\nimport {\n  addContentHighlightBGName,\n  delContentHighlightBGName,\n  diffFontSizeName,\n  getSymbol,\n  NewLineSymbol,\n} from \"@git-diff-view/utils\";\n\nimport { DiffNoNewLine } from \"./DiffNoNewLine\";\n\nimport type { DiffFile, DiffLine, File } from \"@git-diff-view/core\";\n\nconst DiffString = ({\n  rawLine,\n  diffLine,\n  operator,\n  plainLine,\n  enableWrap,\n  enableTemplate,\n}: {\n  rawLine: string;\n  diffLine?: DiffLine;\n  plainLine?: File[\"plainFile\"][number];\n  operator?: \"add\" | \"del\";\n  enableWrap?: boolean;\n  enableTemplate?: boolean;\n}) => {\n  const changes = diffLine?.changes;\n\n  if (changes?.hasLineChange) {\n    const isNewLineSymbolChanged = changes.newLineSymbol;\n\n    if (enableTemplate && !diffLine?.plainTemplate && typeof getPlainDiffTemplate === \"function\") {\n      getPlainDiffTemplate({ diffLine, rawLine, operator });\n    }\n\n    if (enableTemplate && diffLine?.plainTemplate) {\n      return (\n        <span class=\"diff-line-content-raw\">\n          <span data-template innerHTML={diffLine.plainTemplate} />\n          {isNewLineSymbolChanged === NewLineSymbol.NEWLINE && (\n            <span\n              data-no-newline-at-end-of-file-symbol\n              class={enableWrap ? \"block !text-red-500\" : \"inline-block align-middle !text-red-500\"}\n              style={{\n                width: `var(${diffFontSizeName})`,\n                height: `var(${diffFontSizeName})`,\n              }}\n            >\n              <DiffNoNewLine />\n            </span>\n          )}\n        </span>\n      );\n    } else {\n      // TODO remove\n      const range = changes.range;\n      const str1 = rawLine.slice(0, range.location);\n      const str2 = rawLine.slice(range.location, range.location + range.length);\n      const str3 = rawLine.slice(range.location + range.length);\n      const isLast = str2.includes(\"\\n\");\n      const _str2 = isLast ? str2.replace(\"\\n\", \"\").replace(\"\\r\", \"\") : str2;\n      return (\n        <span class=\"diff-line-content-raw\">\n          <span data-range-start={range.location} data-range-end={range.location + range.length}>\n            {str1}\n            <span\n              data-diff-highlight\n              class=\"rounded-[0.2em]\"\n              style={{\n                backgroundColor:\n                  operator === \"add\" ? `var(${addContentHighlightBGName})` : `var(${delContentHighlightBGName})`,\n              }}\n            >\n              {isLast ? (\n                <>\n                  {_str2}\n                  <span data-newline-symbol>{getSymbol(isNewLineSymbolChanged)}</span>\n                </>\n              ) : (\n                str2\n              )}\n            </span>\n            {str3}\n          </span>\n          {isNewLineSymbolChanged === NewLineSymbol.NEWLINE && (\n            <span\n              data-no-newline-at-end-of-file-symbol\n              class={enableWrap ? \"block !text-red-500\" : \"inline-block align-middle !text-red-500\"}\n              style={{\n                width: `var(${diffFontSizeName})`,\n                height: `var(${diffFontSizeName})`,\n              }}\n            >\n              <DiffNoNewLine />\n            </span>\n          )}\n        </span>\n      );\n    }\n  }\n\n  if (enableTemplate && plainLine && !plainLine?.template) {\n    plainLine.template = getPlainLineTemplate(plainLine.value);\n  }\n\n  if (enableTemplate && plainLine?.template) {\n    return (\n      <span class=\"diff-line-content-raw\">\n        <span data-template innerHTML={plainLine.template} />\n      </span>\n    );\n  }\n\n  return <span class=\"diff-line-content-raw\">{rawLine}</span>;\n};\n\nconst DiffSyntax = ({\n  rawLine,\n  diffLine,\n  operator,\n  syntaxLine,\n  enableWrap,\n  enableTemplate,\n}: {\n  rawLine: string;\n  diffLine?: DiffLine;\n  syntaxLine?: File[\"syntaxFile\"][number];\n  operator?: \"add\" | \"del\";\n  enableWrap?: boolean;\n  enableTemplate?: boolean;\n}) => {\n  if (!syntaxLine) {\n    return (\n      <DiffString\n        rawLine={rawLine}\n        diffLine={diffLine}\n        operator={operator}\n        enableWrap={enableWrap}\n        enableTemplate={enableTemplate}\n      />\n    );\n  }\n\n  const changes = diffLine?.changes;\n\n  if (changes?.hasLineChange) {\n    const isNewLineSymbolChanged = changes.newLineSymbol;\n\n    if (enableTemplate && !diffLine?.syntaxTemplate && typeof getSyntaxDiffTemplate === \"function\") {\n      getSyntaxDiffTemplate({ diffLine, syntaxLine, operator });\n    }\n\n    if (enableTemplate && diffLine?.syntaxTemplate) {\n      return (\n        <span class=\"diff-line-syntax-raw\">\n          <span data-template innerHTML={diffLine.syntaxTemplate} />\n          {isNewLineSymbolChanged === NewLineSymbol.NEWLINE && (\n            <span\n              data-no-newline-at-end-of-file-symbol\n              class={enableWrap ? \"block !text-red-500\" : \"inline-block align-middle !text-red-500\"}\n              style={{\n                width: `var(${diffFontSizeName})`,\n                height: `var(${diffFontSizeName})`,\n              }}\n            >\n              <DiffNoNewLine />\n            </span>\n          )}\n        </span>\n      );\n    } else {\n      // TODO remove\n      const range = changes.range;\n\n      return (\n        <span class=\"diff-line-syntax-raw\">\n          <span data-range-start={range.location} data-range-end={range.location + range.length}>\n            {syntaxLine.nodeList?.map(({ node, wrapper }, index) => {\n              if (node.endIndex < range.location || range.location + range.length < node.startIndex) {\n                return (\n                  <span\n                    key={index}\n                    data-start={node.startIndex}\n                    data-end={node.endIndex}\n                    class={wrapper?.properties?.className?.join(\" \")}\n                    style={wrapper?.properties?.style}\n                  >\n                    {node.value}\n                  </span>\n                );\n              } else {\n                const index1 = range.location - node.startIndex;\n                const index2 = index1 < 0 ? 0 : index1;\n                const str1 = node.value.slice(0, index2);\n                const str2 = node.value.slice(index2, index1 + range.length);\n                const str3 = node.value.slice(index1 + range.length);\n                const isStart = str1.length || range.location === node.startIndex;\n                const isEnd = str3.length || node.endIndex === range.location + range.length - 1;\n                const isLast = str2.includes(\"\\n\");\n                const _str2 = isLast ? str2.replace(\"\\n\", \"\").replace(\"\\r\", \"\") : str2;\n                return (\n                  <span\n                    key={index}\n                    data-start={node.startIndex}\n                    data-end={node.endIndex}\n                    class={wrapper?.properties?.className?.join(\" \")}\n                    style={wrapper?.properties?.style}\n                  >\n                    {str1}\n                    <span\n                      data-diff-highlight\n                      style={{\n                        backgroundColor:\n                          operator === \"add\"\n                            ? `var(${addContentHighlightBGName})`\n                            : `var(${delContentHighlightBGName})`,\n                        borderTopLeftRadius: isStart ? \"0.2em\" : undefined,\n                        borderBottomLeftRadius: isStart ? \"0.2em\" : undefined,\n                        borderTopRightRadius: isEnd || isLast ? \"0.2em\" : undefined,\n                        borderBottomRightRadius: isEnd || isLast ? \"0.2em\" : undefined,\n                      }}\n                    >\n                      {isLast ? (\n                        <>\n                          {_str2}\n                          <span data-newline-symbol>{getSymbol(isNewLineSymbolChanged)}</span>\n                        </>\n                      ) : (\n                        str2\n                      )}\n                    </span>\n                    {str3}\n                  </span>\n                );\n              }\n            })}\n          </span>\n          {isNewLineSymbolChanged === NewLineSymbol.NEWLINE && (\n            <span\n              data-no-newline-at-end-of-file-symbol\n              class={enableWrap ? \"block !text-red-500\" : \"inline-block align-middle !text-red-500\"}\n              style={{\n                width: `var(${diffFontSizeName})`,\n                height: `var(${diffFontSizeName})`,\n              }}\n            >\n              <DiffNoNewLine />\n            </span>\n          )}\n        </span>\n      );\n    }\n  }\n\n  if (enableTemplate && !syntaxLine.template) {\n    syntaxLine.template = getSyntaxLineTemplate(syntaxLine);\n  }\n\n  if (enableTemplate && syntaxLine.template) {\n    return (\n      <span class=\"diff-line-syntax-raw\">\n        <span data-template innerHTML={syntaxLine.template} />\n      </span>\n    );\n  }\n\n  return (\n    <span class=\"diff-line-syntax-raw\">\n      {syntaxLine?.nodeList?.map(({ node, wrapper }, index) => (\n        <span\n          key={index}\n          data-start={node.startIndex}\n          data-end={node.endIndex}\n          class={wrapper?.properties?.className?.join(\" \")}\n          style={wrapper?.properties?.style}\n        >\n          {node.value}\n        </span>\n      ))}\n    </span>\n  );\n};\n\nexport const DiffContent = ({\n  diffLine,\n  rawLine,\n  diffFile,\n  plainLine,\n  syntaxLine,\n  enableWrap,\n  enableHighlight,\n}: {\n  rawLine: string;\n  plainLine?: File[\"plainFile\"][number];\n  syntaxLine?: File[\"syntaxFile\"][number];\n  diffLine?: DiffLine;\n  diffFile: DiffFile;\n  enableWrap: boolean;\n  enableHighlight: boolean;\n}) => {\n  const isAdded = diffLine?.type === DiffLineType.Add;\n\n  const isDelete = diffLine?.type === DiffLineType.Delete;\n\n  const isMaxLineLengthToIgnoreSyntax = syntaxLine?.nodeList?.length > 150;\n\n  const isEnableTemplate = diffFile?.getIsEnableTemplate?.() ?? true;\n\n  return (\n    <div\n      class=\"diff-line-content-item pl-[2.0em]\"\n      // data-val={rawLine}\n      style={{\n        whiteSpace: enableWrap ? \"pre-wrap\" : \"pre\",\n        wordBreak: enableWrap ? \"break-all\" : \"initial\",\n      }}\n    >\n      <span\n        data-operator={isAdded ? \"+\" : isDelete ? \"-\" : undefined}\n        class=\"diff-line-content-operator ml-[-1.5em] inline-block w-[1.5em] select-none indent-[0.2em]\"\n      >\n        {isAdded ? \"+\" : isDelete ? \"-\" : \" \"}\n      </span>\n      {enableHighlight && syntaxLine && !isMaxLineLengthToIgnoreSyntax ? (\n        <DiffSyntax\n          operator={isAdded ? \"add\" : isDelete ? \"del\" : undefined}\n          rawLine={rawLine}\n          diffLine={diffLine}\n          syntaxLine={syntaxLine}\n          enableWrap={enableWrap}\n          enableTemplate={isEnableTemplate}\n        />\n      ) : (\n        <DiffString\n          operator={isAdded ? \"add\" : isDelete ? \"del\" : undefined}\n          rawLine={rawLine}\n          diffLine={diffLine}\n          plainLine={plainLine}\n          enableWrap={enableWrap}\n          enableTemplate={isEnableTemplate}\n        />\n      )}\n    </div>\n  );\n};\n", "import { DiffLineType, type DiffFile, checkDiffLineIncludeChange } from \"@git-diff-view/core\";\nimport {\n  getContentBG,\n  getLineNumberBG,\n  plainLineNumberColorName,\n  diffAsideWidthName,\n  emptyBGName,\n  expandLineNumberColorName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { useEnableAddWidget, useEnableHighlight, useOnAddWidgetClick, useSetWidget } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\n\nimport { DiffSplitAddWidget } from \"./DiffAddWidget\";\nimport { DiffContent } from \"./DiffContent\";\nimport { SplitSide } from \"./DiffView\";\n\nexport const DiffSplitContentLine = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const setWidget = useSetWidget();\n\n    const enableAddWidget = useEnableAddWidget();\n\n    const enableHighlight = useEnableHighlight();\n\n    const onAddWidgetClick = useOnAddWidgetClick();\n\n    const currentLine = computed(() =>\n      props.side === SplitSide.old\n        ? props.diffFile.getSplitLeftLine(props.index)\n        : props.diffFile.getSplitRightLine(props.index)\n    );\n\n    const currentLineHasDiff = computed(() => !!currentLine.value?.diff);\n\n    const currentLineHasChange = computed(() => checkDiffLineIncludeChange(currentLine.value?.diff));\n\n    const currentLineHasHidden = computed(() => currentLine.value?.isHidden);\n\n    const currentLineHasContent = computed(() => currentLine.value?.lineNumber);\n\n    const currentSyntaxLine = ref(\n      props.side === SplitSide.old\n        ? props.diffFile.getOldSyntaxLine(currentLine.value?.lineNumber)\n        : props.diffFile.getNewSyntaxLine(currentLine.value?.lineNumber)\n    );\n\n    const currentPlainLine = ref(\n      props.side === SplitSide.old\n        ? props.diffFile.getOldPlainLine(currentLine.value?.lineNumber)\n        : props.diffFile.getNewPlainLine(currentLine.value?.lineNumber)\n    );\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      currentSyntaxLine.value =\n        props.side === SplitSide.old\n          ? diffFile.getOldSyntaxLine(currentLine.value?.lineNumber)\n          : diffFile.getNewSyntaxLine(currentLine.value?.lineNumber);\n\n      currentPlainLine.value =\n        props.side === SplitSide.old\n          ? diffFile.getOldPlainLine(currentLine.value?.lineNumber)\n          : diffFile.getNewPlainLine(currentLine.value?.lineNumber);\n    });\n\n    const onOpenAddWidget = (lineNumber: number, side: SplitSide) => setWidget({ side: side, lineNumber: lineNumber });\n\n    return () => {\n      if (currentLineHasHidden.value) return null;\n\n      const isAdded = currentLine.value?.diff?.type === DiffLineType.Add;\n\n      const isDelete = currentLine.value?.diff?.type === DiffLineType.Delete;\n\n      const contentBG = getContentBG(isAdded, isDelete, currentLineHasDiff.value);\n\n      const lineNumberBG = getLineNumberBG(isAdded, isDelete, currentLineHasDiff.value);\n\n      return (\n        <tr\n          data-line={props.lineNumber}\n          data-state={currentLineHasDiff.value || !currentLineHasContent.value ? \"diff\" : \"plain\"}\n          data-side={SplitSide[props.side]}\n          class={\"diff-line\" + (currentLineHasContent.value ? \" group\" : \"\")}\n        >\n          {currentLineHasContent.value ? (\n            <>\n              <td\n                class={`diff-line-${SplitSide[props.side]}-num sticky left-0 z-[1] w-[1%] min-w-[40px] select-none pl-[10px] pr-[10px] text-right align-top`}\n                style={{\n                  backgroundColor: lineNumberBG,\n                  color: `var(${currentLineHasDiff.value ? plainLineNumberColorName : expandLineNumberColorName})`,\n                  width: `var(${diffAsideWidthName})`,\n                  minWidth: `var(${diffAsideWidthName})`,\n                  maxWidth: `var(${diffAsideWidthName})`,\n                }}\n              >\n                {currentLineHasDiff.value && enableAddWidget.value && (\n                  <DiffSplitAddWidget\n                    index={props.index}\n                    lineNumber={currentLine.value.lineNumber}\n                    side={props.side}\n                    diffFile={props.diffFile}\n                    onWidgetClick={onAddWidgetClick}\n                    className=\"absolute left-[100%] z-[1] translate-x-[-50%]\"\n                    onOpenAddWidget={onOpenAddWidget}\n                  />\n                )}\n                <span\n                  data-line-num={currentLine.value.lineNumber}\n                  style={{ opacity: currentLineHasChange.value ? undefined : 0.5 }}\n                >\n                  {currentLine.value.lineNumber}\n                </span>\n              </td>\n              <td\n                class={`diff-line-${SplitSide[props.side]}-content pr-[10px] align-top`}\n                style={{ backgroundColor: contentBG }}\n              >\n                <DiffContent\n                  enableWrap={false}\n                  diffFile={props.diffFile}\n                  rawLine={currentLine.value?.value || \"\"}\n                  diffLine={currentLine.value?.diff}\n                  plainLine={currentPlainLine.value}\n                  syntaxLine={currentSyntaxLine.value}\n                  enableHighlight={enableHighlight.value}\n                />\n              </td>\n            </>\n          ) : (\n            <td\n              class={`diff-line-${SplitSide[props.side]}-placeholder select-none`}\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            >\n              <span>&ensp;</span>\n            </td>\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitContentLine\", props: [\"diffFile\", \"index\", \"lineNumber\", \"side\"] }\n);\n", "import { ref, watchPostEffect } from \"vue\";\n\nimport { useId, useIsMounted } from \"../context\";\n\nimport type { Ref } from \"vue\";\n\nexport type ObserveElement = HTMLElement & {\n  __observeCallback: Set<() => void>;\n  __observeInstance: ResizeObserver;\n};\n\nexport const useDomWidth = ({ selector, enable }: { selector: Ref<string>; enable: Ref<boolean> }) => {\n  const id = useId();\n\n  const mounted = useIsMounted();\n\n  const width = ref(0);\n\n  const observeWidth = (onCancel: (cb: () => void) => void) => {\n    if (!mounted.value) return;\n\n    if (enable.value) {\n      const container = document.querySelector(`#diff-root${id.value}`);\n\n      const wrapper = container?.querySelector(selector.value);\n\n      if (!wrapper) return;\n\n      const typedWrapper = wrapper as ObserveElement;\n\n      const cb = () => {\n        const rect = wrapper?.getBoundingClientRect();\n        width.value = rect?.width ?? 0;\n      };\n\n      cb();\n\n      const cleanCb = () => {\n        typedWrapper.__observeCallback.delete(cb);\n        if (typedWrapper.__observeCallback.size === 0) {\n          typedWrapper.__observeInstance?.disconnect();\n          typedWrapper.removeAttribute(\"data-observe\");\n          delete typedWrapper.__observeCallback;\n          delete typedWrapper.__observeInstance;\n        }\n      };\n\n      if (typedWrapper.__observeCallback) {\n        typedWrapper.__observeCallback.add(cb);\n\n        onCancel(() => cleanCb());\n\n        return;\n      }\n\n      typedWrapper.__observeCallback = new Set();\n\n      typedWrapper.__observeCallback.add(cb);\n\n      const observer = new ResizeObserver(() => typedWrapper.__observeCallback.forEach((cb) => cb()));\n\n      typedWrapper.__observeInstance = observer;\n\n      observer.observe(typedWrapper);\n\n      typedWrapper.setAttribute(\"data-observe\", \"height\");\n\n      onCancel(() => cleanCb());\n    }\n  };\n\n  watchPostEffect((onCancel) => observeWidth(onCancel));\n\n  return width;\n};\n", "import { type Ref, watchPostEffect } from \"vue\";\n\nimport { useId, useIsMounted } from \"../context\";\n\nimport type { ObserveElement } from \"./useDomWidth\";\n\n// TODO\nexport const useSyncHeight = ({\n  selector,\n  wrapper,\n  side,\n  enable,\n}: {\n  selector: Ref<string>;\n  wrapper: Ref<string>;\n  side: Ref<string>;\n  enable: Ref<boolean>;\n}) => {\n  const id = useId();\n\n  const isMounted = useIsMounted();\n\n  const observeHeight = (onCancel: (cb: () => void) => void) => {\n    if (!isMounted.value) return;\n\n    if (enable.value) {\n      let clean = () => {};\n\n      const container = document.querySelector(`#diff-root${id.value}`);\n\n      const elements = Array.from(container?.querySelectorAll(selector.value) || []);\n\n      const wrappers = wrapper.value ? Array.from(container?.querySelectorAll(wrapper?.value) || []) : elements;\n\n      if (elements.length === 2 && wrappers.length === 2) {\n        const ele1 = elements[0] as HTMLElement;\n        const ele2 = elements[1] as HTMLElement;\n\n        const wrapper1 = wrappers[0] as HTMLElement;\n        const wrapper2 = wrappers[1] as HTMLElement;\n\n        const target = ele1.getAttribute(\"data-side\") === side.value ? ele1 : ele2;\n\n        const typedTarget = target as ObserveElement;\n\n        const cb = () => {\n          ele1.style.height = \"auto\";\n          ele2.style.height = \"auto\";\n          const rect1 = ele1.getBoundingClientRect();\n          const rect2 = ele2.getBoundingClientRect();\n          const maxHeight = Math.max(rect1.height, rect2.height);\n          wrapper1.style.height = maxHeight + \"px\";\n          wrapper2.style.height = maxHeight + \"px\";\n          wrapper1.setAttribute(\"data-sync-height\", String(maxHeight));\n          wrapper2.setAttribute(\"data-sync-height\", String(maxHeight));\n        };\n\n        cb();\n\n        const cleanCb = () => {\n          typedTarget.__observeCallback.delete(cb);\n          if (typedTarget.__observeCallback.size === 0) {\n            typedTarget.__observeInstance?.disconnect();\n            target.removeAttribute(\"data-observe\");\n            delete typedTarget.__observeCallback;\n            delete typedTarget.__observeInstance;\n          }\n        };\n\n        if (typedTarget.__observeCallback) {\n          typedTarget.__observeCallback.add(cb);\n\n          clean = cleanCb;\n          return;\n        }\n\n        typedTarget.__observeCallback = new Set();\n\n        typedTarget.__observeCallback.add(cb);\n\n        const observer = new ResizeObserver(() => typedTarget.__observeCallback.forEach((cb) => cb()));\n\n        typedTarget.__observeInstance = observer;\n\n        observer.observe(target);\n\n        target.setAttribute(\"data-observe\", \"height\");\n\n        clean = cleanCb;\n      }\n\n      onCancel(() => clean());\n    }\n  };\n\n  watchPostEffect(observeHeight);\n};\n", "import { emptyBGName } from \"@git-diff-view/utils\";\nimport { computed, defineComponent } from \"vue\";\n\nimport { useExtendData, useSlots } from \"../context\";\nimport { useDomWidth } from \"../hooks/useDomWidth\";\nimport { useSyncHeight } from \"../hooks/useSyncHeight\";\n\nimport { SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitExtendLine = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const extendData = useExtendData();\n\n    const slots = useSlots();\n\n    const lineSelector = computed(() => `div[data-line=\"${props.lineNumber}-extend-content\"]`);\n\n    const lineWrapperSelector = computed(() => `tr[data-line=\"${props.lineNumber}-extend\"]`);\n\n    const wrapperSelector = computed(() =>\n      props.side === SplitSide.old ? \".old-diff-table-wrapper\" : \".new-diff-table-wrapper\"\n    );\n\n    const oldLine = computed(() => props.diffFile.getSplitLeftLine(props.index));\n\n    const newLine = computed(() => props.diffFile.getSplitRightLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const oldLineExtend = computed(() => extendData.value?.oldFile?.[oldLine.value?.lineNumber]);\n\n    const newLineExtend = computed(() => extendData.value?.newFile?.[newLine.value.lineNumber]);\n\n    const currentItem = computed(() => (props.side === SplitSide.old ? oldLine.value : newLine.value));\n\n    const currentIsHidden = computed(() => currentItem.value.isHidden);\n\n    const currentExtend = computed(() => (props.side === SplitSide.old ? oldLineExtend.value : newLineExtend.value));\n\n    const currentLineNumber = computed(() =>\n      props.side === SplitSide.old ? oldLine.value.lineNumber : newLine.value.lineNumber\n    );\n\n    const currentIsShow = computed(() =>\n      Boolean(\n        (oldLineExtend.value || newLineExtend.value) && (!currentIsHidden.value || enableExpand.value) && slots.extend\n      )\n    );\n\n    const currentEnable = computed(\n      () => (props.side === SplitSide.old ? !!oldLineExtend.value : !!newLineExtend.value) && currentIsShow.value\n    );\n\n    const extendSide = computed(\n      () => SplitSide[currentExtend.value ? props.side : props.side === SplitSide.new ? SplitSide.old : SplitSide.new]\n    );\n\n    useSyncHeight({\n      selector: lineSelector,\n      wrapper: lineWrapperSelector,\n      side: extendSide,\n      enable: currentIsShow,\n    });\n\n    const width = useDomWidth({\n      selector: wrapperSelector,\n      enable: currentEnable,\n    });\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      return (\n        <tr\n          data-line={`${props.lineNumber}-extend`}\n          data-state=\"extend\"\n          data-side={SplitSide[props.side]}\n          class=\"diff-line diff-line-extend\"\n        >\n          {currentExtend.value ? (\n            <td class={`diff-line-extend-${SplitSide[props.side]}-content p-0`} colspan={2}>\n              <div\n                data-line={`${props.lineNumber}-extend-content`}\n                data-side={SplitSide[props.side]}\n                class=\"diff-line-extend-wrapper sticky left-0 z-[1]\"\n                style={{ width: width.value + \"px\" }}\n              >\n                {width.value > 0 &&\n                  slots.extend?.({\n                    diffFile: props.diffFile,\n                    side: props.side,\n                    lineNumber: currentLineNumber.value,\n                    data: currentExtend.value.data,\n                    onUpdate: props.diffFile.notifyAll,\n                  })}\n              </div>\n            </td>\n          ) : (\n            <td\n              class={`diff-line-extend-${SplitSide[props.side]}-placeholder select-none p-0`}\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            >\n              <div data-line={`${props.lineNumber}-extend-content`} data-side={SplitSide[props.side]} />\n            </td>\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitExtendLine\", props: [\"index\", \"diffFile\", \"lineNumber\", \"side\"] }\n);\n", "export const ExpandDown = ({ className }: { className: string }) => {\n  return (\n    <svg aria-hidden=\"true\" height=\"16\" viewBox=\"0 0 16 16\" version=\"1.1\" width=\"16\" class={className}>\n      <path d=\"m8.177 14.323 2.896-2.896a.25.25 0 0 0-.177-.427H8.75V7.764a.75.75 0 1 0-1.5 0V11H5.104a.25.25 0 0 0-.177.427l2.896 2.896a.25.25 0 0 0 .354 0ZM2.25 5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 4.25a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5a.75.75 0 0 1 .75.75ZM8.25 5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 4.25a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5a.75.75 0 0 1 .75.75Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\"></path>\n    </svg>\n  );\n};\n\nexport const ExpandUp = ({ className }: { className?: string }) => {\n  return (\n    <svg aria-hidden=\"true\" height=\"16\" viewBox=\"0 0 16 16\" version=\"1.1\" width=\"16\" class={className}>\n      <path d=\"M7.823 1.677 4.927 4.573A.25.25 0 0 0 5.104 5H7.25v3.236a.75.75 0 1 0 1.5 0V5h2.146a.25.25 0 0 0 .177-.427L8.177 1.677a.25.25 0 0 0-.354 0ZM13.75 11a.75.75 0 0 0 0 1.5h.5a.75.75 0 0 0 0-1.5h-.5Zm-3.75.75a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75ZM7.75 11a.75.75 0 0 0 0 1.5h.5a.75.75 0 0 0 0-1.5h-.5ZM4 11.75a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75ZM1.75 11a.75.75 0 0 0 0 1.5h.5a.75.75 0 0 0 0-1.5h-.5Z\"></path>\n    </svg>\n  );\n};\n\nexport const ExpandAll = ({ className }: { className?: string }) => {\n  return (\n    <svg aria-hidden=\"true\" height=\"16\" viewBox=\"0 0 16 16\" version=\"1.1\" width=\"16\" class={className}>\n      <path d=\"m8.177.677 2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25a.75.75 0 0 1-1.5 0V4H5.104a.25.25 0 0 1-.177-.427L7.823.677a.25.25 0 0 1 .354 0ZM7.25 10.75a.75.75 0 0 1 1.5 0V12h2.146a.25.25 0 0 1 .177.427l-2.896 2.896a.25.25 0 0 1-.354 0l-2.896-2.896A.25.25 0 0 1 5.104 12H7.25v-1.25Zm-5-2a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\"></path>\n    </svg>\n  );\n};\n", "import { composeLen, type DiffFile } from \"@git-diff-view/core\";\nimport {\n  hunkContentBGName,\n  hunkContentColorName,\n  hunkLineNumberBGName,\n  plainLineNumberColorName,\n  diffAsideWidthName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { useMode } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\nimport { useSyncHeight } from \"../hooks/useSyncHeight\";\n\nimport { ExpandAll, ExpandDown, ExpandUp } from \"./DiffExpand\";\nimport { DiffModeEnum, SplitSide } from \"./DiffView\";\n\nconst DiffSplitHunkLineGitHub = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const currentHunk = computed(() => props.diffFile.getSplitHunkLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const couldExpand = computed(() => enableExpand.value && currentHunk.value && currentHunk.value.splitInfo);\n\n    const lineSelector = computed(() => `tr[data-line=\"${props.lineNumber}-hunk\"]`);\n\n    const currentShowExpand = computed(() => props.side === SplitSide.old);\n\n    const currentSyncHeightSide = computed(() => SplitSide[SplitSide.old]);\n\n    const currentIsFirstLine = computed(() => currentHunk.value && currentHunk.value.isFirst);\n\n    const currentIsLastLine = computed(() => currentHunk.value && currentHunk.value.isLast);\n\n    const currentIsPureHunk = computed(\n      () => currentHunk.value && props.diffFile._getIsPureDiffRender() && !currentHunk.value.splitInfo\n    );\n\n    const currentShowExpandAll = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen\n    );\n\n    const currentIsShow = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex\n    );\n\n    useSubscribeDiffFile(props, () => {\n      currentShowExpandAll.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen;\n\n      currentIsShow.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex;\n    });\n\n    const currentEnableSyncHeight = computed(() => props.side === SplitSide.new && currentIsShow.value);\n\n    useSyncHeight({\n      selector: lineSelector,\n      wrapper: lineSelector,\n      side: currentSyncHeightSide,\n      enable: currentEnableSyncHeight,\n    });\n\n    return () => {\n      if (!currentIsShow.value && !currentIsPureHunk.value) return null;\n\n      return (\n        <tr\n          data-line={`${props.lineNumber}-hunk`}\n          data-state=\"hunk\"\n          data-side={SplitSide[props.side]}\n          style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          class=\"diff-line diff-line-hunk\"\n        >\n          {currentShowExpand.value ? (\n            <>\n              <td\n                class=\"diff-line-hunk-action sticky left-0 w-[1%] min-w-[40px] select-none p-[1px]\"\n                style={{\n                  backgroundColor: `var(${hunkLineNumberBGName})`,\n                  color: `var(${plainLineNumberColorName})`,\n                  width: `var(${diffAsideWidthName})`,\n                  minWidth: `var(${diffAsideWidthName})`,\n                  maxWidth: `var(${diffAsideWidthName})`,\n                }}\n              >\n                {couldExpand.value ? (\n                  currentIsFirstLine.value ? (\n                    <button\n                      class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                      title=\"Expand Up\"\n                      data-title=\"Expand Up\"\n                      onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                    >\n                      <ExpandUp className=\"fill-current\" />\n                    </button>\n                  ) : currentIsLastLine.value ? (\n                    <button\n                      class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                      title=\"Expand Down\"\n                      data-title=\"Expand Down\"\n                      onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                    >\n                      <ExpandDown className=\"fill-current\" />\n                    </button>\n                  ) : currentShowExpandAll.value ? (\n                    <button\n                      class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                      title=\"Expand All\"\n                      data-title=\"Expand All\"\n                      onClick={() => props.diffFile.onSplitHunkExpand(\"all\", props.index)}\n                    >\n                      <ExpandAll className=\"fill-current\" />\n                    </button>\n                  ) : (\n                    <>\n                      <button\n                        class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                        title=\"Expand Down\"\n                        data-title=\"Expand Down\"\n                        onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                      >\n                        <ExpandDown className=\"fill-current\" />\n                      </button>\n                      <button\n                        class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                        title=\"Expand Up\"\n                        data-title=\"Expand Up\"\n                        onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                      >\n                        <ExpandUp className=\"fill-current\" />\n                      </button>\n                    </>\n                  )\n                ) : (\n                  <div class=\"min-h-[28px]\">&ensp;</div>\n                )}\n              </td>\n              <td\n                class=\"diff-line-hunk-content pr-[10px] align-middle\"\n                style={{ backgroundColor: `var(${hunkContentBGName})` }}\n              >\n                <div\n                  class=\"pl-[1.5em]\"\n                  style={{\n                    color: `var(${hunkContentColorName})`,\n                  }}\n                >\n                  {currentHunk.value.splitInfo?.plainText || currentHunk.value.text}\n                </div>\n              </td>\n            </>\n          ) : (\n            <td\n              class=\"diff-line-hunk-placeholder select-none\"\n              colspan={2}\n              style={{ backgroundColor: `var(${hunkContentBGName})` }}\n            >\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            </td>\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\", \"side\"] }\n);\n\nconst DiffSplitHunkLineGitLab = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const currentHunk = computed(() => props.diffFile.getSplitHunkLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const lineSelector = computed(() => `tr[data-line=\"${props.lineNumber}-hunk\"]`);\n\n    const couldExpand = computed(() => enableExpand.value && currentHunk.value && currentHunk.value.splitInfo);\n\n    const currentShowExpandAll = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen\n    );\n\n    const currentIsFirstLine = computed(() => currentHunk.value && currentHunk.value.isFirst);\n\n    const currentIsPureHunk = computed(\n      () => currentHunk.value && props.diffFile._getIsPureDiffRender() && !currentHunk.value.splitInfo\n    );\n\n    const currentIsLastLine = computed(() => currentHunk.value && currentHunk.value.isLast);\n\n    const currentIsShow = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex\n    );\n\n    const currentSyncHeightSide = computed(() => SplitSide[props.side]);\n\n    const currentEnableSyncHeight = computed(() => currentIsShow.value);\n\n    useSyncHeight({\n      selector: lineSelector,\n      wrapper: lineSelector,\n      side: currentSyncHeightSide,\n      enable: currentEnableSyncHeight,\n    });\n\n    useSubscribeDiffFile(props, () => {\n      currentShowExpandAll.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen;\n\n      currentIsShow.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex;\n    });\n\n    return () => {\n      if (!currentIsShow.value && !currentIsPureHunk.value) return null;\n\n      return (\n        <tr\n          data-line={`${props.lineNumber}-hunk`}\n          data-state=\"hunk\"\n          data-side={SplitSide[props.side]}\n          style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          class=\"diff-line diff-line-hunk\"\n        >\n          <td\n            class=\"diff-line-hunk-action sticky left-0 w-[1%] min-w-[40px] select-none p-[1px]\"\n            style={{\n              backgroundColor: `var(${hunkLineNumberBGName})`,\n              color: `var(${plainLineNumberColorName})`,\n              width: `var(${diffAsideWidthName})`,\n              minWidth: `var(${diffAsideWidthName})`,\n              maxWidth: `var(${diffAsideWidthName})`,\n            }}\n          >\n            {couldExpand.value ? (\n              currentIsFirstLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Up\"\n                  data-title=\"Expand Up\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                >\n                  <ExpandUp className=\"fill-current\" />\n                </button>\n              ) : currentIsLastLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Down\"\n                  data-title=\"Expand Down\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                >\n                  <ExpandDown className=\"fill-current\" />\n                </button>\n              ) : currentShowExpandAll.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand All\"\n                  data-title=\"Expand All\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"all\", props.index)}\n                >\n                  <ExpandAll className=\"fill-current\" />\n                </button>\n              ) : (\n                <>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Down\"\n                    data-title=\"Expand Down\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                  >\n                    <ExpandDown className=\"fill-current\" />\n                  </button>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Up\"\n                    data-title=\"Expand Up\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                  >\n                    <ExpandUp className=\"fill-current\" />\n                  </button>\n                </>\n              )\n            ) : (\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            )}\n          </td>\n          <td\n            class=\"diff-line-hunk-content pr-[10px] align-middle\"\n            style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          >\n            <div\n              class=\"pl-[1.5em]\"\n              style={{\n                color: `var(${hunkContentColorName})`,\n              }}\n            >\n              {currentHunk.value.splitInfo?.plainText || currentHunk.value.text}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\", \"side\"] }\n);\n\nexport const DiffSplitHunkLine = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const diffViewMode = useMode();\n\n    return () => {\n      if (diffViewMode.value === DiffModeEnum.SplitGitHub || diffViewMode.value === DiffModeEnum.Split) {\n        return (\n          <DiffSplitHunkLineGitHub\n            index={props.index}\n            side={props.side}\n            diffFile={props.diffFile}\n            lineNumber={props.lineNumber}\n          />\n        );\n      } else {\n        return (\n          <DiffSplitHunkLineGitLab\n            index={props.index}\n            side={props.side}\n            diffFile={props.diffFile}\n            lineNumber={props.lineNumber}\n          />\n        );\n      }\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\", \"side\"] }\n);\n", "import { emptyBGName } from \"@git-diff-view/utils\";\nimport { computed, defineComponent } from \"vue\";\n\nimport { useSetWidget, useSlots, useWidget } from \"../context\";\nimport { useDomWidth } from \"../hooks/useDomWidth\";\nimport { useSyncHeight } from \"../hooks/useSyncHeight\";\n\nimport { SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitWidgetLine = defineComponent(\n  (props: { index: number; side: SplitSide; diffFile: DiffFile; lineNumber: number }) => {\n    const slots = useSlots();\n\n    const widget = useWidget();\n\n    const setWidget = useSetWidget();\n\n    const oldLine = computed(() => props.diffFile.getSplitLeftLine(props.index));\n\n    const newLine = computed(() => props.diffFile.getSplitRightLine(props.index));\n\n    const oldLineWidget = computed(\n      () =>\n        oldLine.value.lineNumber &&\n        widget.value.side === SplitSide.old &&\n        widget.value.lineNumber === oldLine.value.lineNumber\n    );\n\n    const newLineWidget = computed(\n      () =>\n        newLine.value.lineNumber &&\n        widget.value.side === SplitSide.new &&\n        widget.value.lineNumber === newLine.value.lineNumber\n    );\n\n    const currentLine = computed(() => (props.side === SplitSide.old ? oldLine.value : newLine.value));\n\n    const currentIsHidden = computed(() => currentLine.value.isHidden);\n\n    const lineSelector = computed(() => `div[data-line=\"${props.lineNumber}-widget-content\"]`);\n\n    const lineWrapperSelector = computed(() => `tr[data-line=\"${props.lineNumber}-widget\"]`);\n\n    const wrapperSelector = computed(() =>\n      props.side === SplitSide.old ? \".old-diff-table-wrapper\" : \".new-diff-table-wrapper\"\n    );\n\n    const currentWidget = computed(() => (props.side === SplitSide.old ? oldLineWidget.value : newLineWidget.value));\n\n    const observeSide = computed(\n      () => SplitSide[currentWidget ? props.side : props.side === SplitSide.old ? SplitSide.new : SplitSide.old]\n    );\n\n    const currentIsShow = computed(\n      () => (!!oldLineWidget.value || !!newLineWidget.value) && !currentIsHidden.value && !!slots.widget\n    );\n\n    const currentEnable = computed(() => currentWidget.value && !!currentIsShow.value);\n\n    const onCloseWidget = () => setWidget({});\n\n    useSyncHeight({\n      selector: lineSelector,\n      wrapper: lineWrapperSelector,\n      side: observeSide,\n      enable: currentIsShow,\n    });\n\n    const width = useDomWidth({\n      selector: wrapperSelector,\n      enable: currentEnable,\n    });\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      return (\n        <tr\n          data-line={`${props.lineNumber}-widget`}\n          data-state=\"widget\"\n          data-side={SplitSide[props.side]}\n          class=\"diff-line diff-line-widget\"\n        >\n          {currentWidget.value ? (\n            <td class={`diff-line-widget-${SplitSide[props.side]}-content p-0`} colspan={2}>\n              <div\n                data-line={`${props.lineNumber}-widget-content`}\n                data-side={SplitSide[props.side]}\n                class=\"diff-line-widget-wrapper sticky left-0 z-[1]\"\n                style={{ width: width.value + \"px\" }}\n              >\n                {width.value > 0 &&\n                  slots.widget?.({\n                    diffFile: props.diffFile,\n                    side: props.side,\n                    lineNumber: currentLine.value.lineNumber,\n                    onClose: onCloseWidget,\n                  })}\n              </div>\n            </td>\n          ) : (\n            <td\n              class={`diff-line-widget-${SplitSide[props.side]}-placeholder select-none p-0`}\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            >\n              <div data-line={`${props.lineNumber}-widget-content`} data-side={SplitSide[props.side]} />\n            </td>\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitWidgetLine\", props: [\"diffFile\", \"index\", \"lineNumber\", \"side\"] }\n);\n", "import { getSplitContentLines } from \"@git-diff-view/core\";\nimport {\n  diffAsideWidthName,\n  diffFontSizeName,\n  removeAllSelection,\n  syncScroll,\n  borderColorName,\n} from \"@git-diff-view/utils\";\nimport { Fragment, computed, defineComponent, ref, watchPostEffect } from \"vue\";\n\nimport { useFontSize } from \"../context\";\nimport { useIsMounted } from \"../hooks/useIsMounted\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\nimport { useTextWidth } from \"../hooks/useTextWidth\";\n\nimport { DiffSplitContentLine } from \"./DiffSplitContentLineNormal\";\nimport { DiffSplitExtendLine } from \"./DiffSplitExtendLineNormal\";\nimport { DiffSplitHunkLine } from \"./DiffSplitHunkLineNormal\";\nimport { DiffSplitWidgetLine } from \"./DiffSplitWidgetLineNormal\";\nimport { SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nconst DiffSplitViewTable = defineComponent(\n  (props: {\n    side: SplitSide;\n    diffFile: DiffFile;\n    onSelect?: (side?: SplitSide) => void;\n    selectState: { current?: SplitSide };\n  }) => {\n    const className = computed(() => (props.side === SplitSide.new ? \"new-diff-table\" : \"old-diff-table\"));\n\n    const lines = ref(getSplitContentLines(props.diffFile));\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      lines.value = getSplitContentLines(diffFile);\n    });\n\n    const selectState = props.selectState;\n\n    const onMouseDown = (e: MouseEvent) => {\n      let ele = e.target as HTMLElement;\n\n      if (ele && ele?.nodeName === \"BUTTON\") {\n        removeAllSelection();\n        return;\n      }\n\n      while (ele && ele instanceof HTMLElement) {\n        const state = ele.getAttribute(\"data-state\");\n        if (state) {\n          if (state === \"extend\" || state === \"hunk\" || state === \"widget\") {\n            if (selectState.current !== undefined) {\n              selectState.current = undefined;\n              props.onSelect?.(undefined);\n              removeAllSelection();\n            }\n          } else {\n            if (selectState.current !== props.side) {\n              selectState.current = props.side;\n              props.onSelect?.(props.side);\n              removeAllSelection();\n            }\n          }\n          return;\n        }\n        ele = ele.parentElement;\n      }\n    };\n\n    return () => {\n      return (\n        <table class={className.value + \" w-full border-collapse border-spacing-0\"} data-mode={SplitSide[props.side]}>\n          <colgroup>\n            <col class={`diff-table-${SplitSide[props.side]}-num-col`} />\n            <col class={`diff-table-${SplitSide[props.side]}-content-col`} />\n          </colgroup>\n          <thead class=\"hidden\">\n            <tr>\n              <th scope=\"col\">{SplitSide[props.side]} line number</th>\n              <th scope=\"col\">{SplitSide[props.side]} line content</th>\n            </tr>\n          </thead>\n          <tbody class=\"diff-table-body leading-[1.4]\" onMousedown={onMouseDown}>\n            {lines.value.map((item) => (\n              <Fragment key={item.index}>\n                <DiffSplitHunkLine\n                  index={item.index}\n                  side={props.side}\n                  lineNumber={item.lineNumber}\n                  diffFile={props.diffFile}\n                />\n                <DiffSplitContentLine\n                  index={item.index}\n                  side={props.side}\n                  lineNumber={item.lineNumber}\n                  diffFile={props.diffFile}\n                />\n                <DiffSplitWidgetLine\n                  index={item.index}\n                  side={props.side}\n                  lineNumber={item.lineNumber}\n                  diffFile={props.diffFile}\n                />\n                <DiffSplitExtendLine\n                  index={item.index}\n                  side={props.side}\n                  lineNumber={item.lineNumber}\n                  diffFile={props.diffFile}\n                />\n              </Fragment>\n            ))}\n            <DiffSplitHunkLine\n              side={props.side}\n              index={props.diffFile.splitLineLength}\n              lineNumber={props.diffFile.splitLineLength}\n              diffFile={props.diffFile}\n            />\n          </tbody>\n        </table>\n      );\n    };\n  },\n  { name: \"DiffSplitViewTable\", props: [\"diffFile\", \"side\", \"onSelect\", \"selectState\"] }\n);\n\nexport const DiffSplitViewNormal = defineComponent(\n  (props: { diffFile: DiffFile }) => {\n    const isMounted = useIsMounted();\n\n    const ref1 = ref<HTMLDivElement>();\n\n    const ref2 = ref<HTMLDivElement>();\n\n    const styleRef = ref<HTMLStyleElement>();\n\n    const maxText = computed(() => Math.max(props.diffFile.splitLineLength, props.diffFile.fileLineLength).toString());\n\n    const initSyncScroll = (onClean: (cb: () => void) => void) => {\n      if (!isMounted.value) return;\n      const left = ref1.value;\n      const right = ref2.value;\n      if (!left || !right) return;\n      const clean = syncScroll(left, right);\n      onClean(clean);\n    };\n\n    watchPostEffect(initSyncScroll);\n\n    const onSelect = (side?: SplitSide) => {\n      const ele = styleRef.value;\n\n      if (!ele) return;\n\n      if (!side) {\n        ele.textContent = \"\";\n      } else {\n        const id = `diff-root${props.diffFile.getId()}`;\n        ele.textContent = `#${id} [data-state=\"extend\"] {user-select: none} \\n#${id} [data-state=\"hunk\"] {user-select: none} \\n#${id} [data-state=\"widget\"] {user-select: none}`;\n      }\n    };\n\n    const fontSize = useFontSize();\n\n    const selectState = { current: undefined as SplitSide | undefined };\n\n    const font = computed(() => ({ fontSize: fontSize.value + \"px\", fontFamily: \"Menlo, Consolas, monospace\" }));\n\n    const width = useTextWidth({ text: maxText, font });\n\n    const computedWidth = computed(() => Math.max(40, width.value + 25));\n\n    return () => {\n      return (\n        <div class=\"split-diff-view split-diff-view-normal flex w-full basis-[50%]\">\n          <style data-select-style ref={styleRef} />\n          <div\n            class=\"old-diff-table-wrapper diff-table-scroll-container w-full overflow-x-auto overflow-y-hidden\"\n            ref={ref1}\n            style={{\n              [diffAsideWidthName]: `${Math.round(computedWidth.value)}px`,\n              overscrollBehaviorX: \"none\",\n              fontFamily: \"Menlo, Consolas, monospace\",\n              fontSize: `var(${diffFontSizeName})`,\n            }}\n          >\n            <DiffSplitViewTable\n              side={SplitSide.old}\n              diffFile={props.diffFile}\n              onSelect={onSelect}\n              selectState={selectState}\n            />\n          </div>\n          <div class=\"diff-split-line w-[1.5px]\" style={{ backgroundColor: `var(${borderColorName})` }} />\n          <div\n            class=\"new-diff-table-wrapper diff-table-scroll-container w-full overflow-x-auto overflow-y-hidden\"\n            ref={ref2}\n            style={{\n              [diffAsideWidthName]: `${Math.round(computedWidth.value)}px`,\n              overscrollBehaviorX: \"none\",\n              fontFamily: \"Menlo, Consolas, monospace\",\n              fontSize: `var(${diffFontSizeName})`,\n            }}\n          >\n            <DiffSplitViewTable\n              side={SplitSide.new}\n              diffFile={props.diffFile}\n              onSelect={onSelect}\n              selectState={selectState}\n            />\n          </div>\n        </div>\n      );\n    };\n  },\n  { name: \"DiffSplitViewNormal\", props: [\"diffFile\"] }\n);\n", "import { DiffLineType, type DiffFile, checkDiffLineIncludeChange } from \"@git-diff-view/core\";\nimport {\n  borderColorName,\n  emptyBGName,\n  expandLineNumberColorName,\n  getContentBG,\n  getLineNumberBG,\n  plainLineNumberColorName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { useEnableAddWidget, useEnableHighlight, useOnAddWidgetClick, useSetWidget } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\n\nimport { DiffSplitAddWidget } from \"./DiffAddWidget\";\nimport { DiffContent } from \"./DiffContent\";\nimport { SplitSide } from \"./DiffView\";\n\nexport const DiffSplitContentLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const setWidget = useSetWidget();\n\n    const enableAddWidget = useEnableAddWidget();\n\n    const enableHighlight = useEnableHighlight();\n\n    const onAddWidgetClick = useOnAddWidgetClick();\n\n    const oldLine = computed(() => props.diffFile.getSplitLeftLine(props.index));\n\n    const newLine = computed(() => props.diffFile.getSplitRightLine(props.index));\n\n    const oldSyntaxLine = ref(props.diffFile.getOldSyntaxLine(oldLine.value?.lineNumber));\n\n    const newSyntaxLine = ref(props.diffFile.getNewSyntaxLine(newLine.value?.lineNumber));\n\n    const oldPlainLine = ref(props.diffFile.getOldPlainLine(oldLine.value.lineNumber));\n\n    const newPlainLine = ref(props.diffFile.getNewPlainLine(newLine.value.lineNumber));\n\n    const hasDiff = computed(() => !!oldLine.value?.diff || !!newLine.value?.diff);\n\n    const hasChange = computed(\n      () => checkDiffLineIncludeChange(oldLine.value?.diff) || checkDiffLineIncludeChange(newLine.value?.diff)\n    );\n\n    const hasHidden = computed(() => oldLine.value?.isHidden && newLine.value?.isHidden);\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      oldSyntaxLine.value = diffFile.getOldSyntaxLine(oldLine.value?.lineNumber);\n\n      newSyntaxLine.value = diffFile.getNewSyntaxLine(newLine.value?.lineNumber);\n\n      oldPlainLine.value = diffFile.getOldPlainLine(oldLine.value.lineNumber);\n\n      newPlainLine.value = diffFile.getNewPlainLine(newLine.value.lineNumber);\n    });\n\n    const onOpenAddWidget = (lineNumber: number, side: SplitSide) => setWidget({ side: side, lineNumber: lineNumber });\n\n    return () => {\n      if (hasHidden.value) return null;\n\n      const hasOldLine = !!oldLine.value?.lineNumber;\n\n      const hasNewLine = !!newLine.value?.lineNumber;\n\n      const oldLineIsDelete = oldLine.value?.diff?.type === DiffLineType.Delete;\n\n      const newLineIsAdded = newLine.value?.diff?.type === DiffLineType.Add;\n\n      const oldLineContentBG = getContentBG(false, oldLineIsDelete, hasDiff.value);\n\n      const oldLineNumberBG = getLineNumberBG(false, oldLineIsDelete, hasDiff.value);\n\n      const newLineContentBG = getContentBG(newLineIsAdded, false, hasDiff.value);\n\n      const newLineNumberBG = getLineNumberBG(newLineIsAdded, false, hasDiff.value);\n\n      return (\n        <tr data-line={props.lineNumber} data-state={hasDiff.value ? \"diff\" : \"plain\"} class=\"diff-line\">\n          {hasOldLine ? (\n            <>\n              <td\n                class=\"diff-line-old-num group relative w-[1%] min-w-[40px] select-none pl-[10px] pr-[10px] text-right align-top\"\n                style={{\n                  backgroundColor: oldLineNumberBG,\n                  color: `var(${hasDiff.value ? plainLineNumberColorName : expandLineNumberColorName})`,\n                }}\n              >\n                {hasDiff.value && enableAddWidget.value && (\n                  <DiffSplitAddWidget\n                    index={props.index}\n                    lineNumber={oldLine.value.lineNumber}\n                    side={SplitSide.old}\n                    diffFile={props.diffFile}\n                    onWidgetClick={onAddWidgetClick}\n                    className=\"absolute left-[100%] z-[1] translate-x-[-50%]\"\n                    onOpenAddWidget={onOpenAddWidget}\n                  />\n                )}\n                <span data-line-num={oldLine.value.lineNumber} style={{ opacity: hasChange.value ? undefined : 0.5 }}>\n                  {oldLine.value.lineNumber}\n                </span>\n              </td>\n              <td\n                class=\"diff-line-old-content group relative pr-[10px] align-top\"\n                style={{ backgroundColor: oldLineContentBG }}\n                data-side={SplitSide[SplitSide.old]}\n              >\n                {hasDiff.value && enableAddWidget.value && (\n                  <DiffSplitAddWidget\n                    index={props.index}\n                    lineNumber={oldLine.value.lineNumber}\n                    side={SplitSide.old}\n                    diffFile={props.diffFile}\n                    onWidgetClick={onAddWidgetClick}\n                    className=\"absolute right-[100%] z-[1] translate-x-[50%]\"\n                    onOpenAddWidget={onOpenAddWidget}\n                  />\n                )}\n                <DiffContent\n                  enableWrap={true}\n                  diffFile={props.diffFile}\n                  rawLine={oldLine.value?.value}\n                  diffLine={oldLine.value?.diff}\n                  plainLine={oldPlainLine.value}\n                  syntaxLine={oldSyntaxLine.value}\n                  enableHighlight={enableHighlight.value}\n                />\n              </td>\n            </>\n          ) : (\n            <td\n              class=\"diff-line-old-placeholder select-none\"\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            >\n              <span>&ensp;</span>\n            </td>\n          )}\n          {hasNewLine ? (\n            <>\n              <td\n                class=\"diff-line-new-num group relative w-[1%] min-w-[40px] select-none border-l-[1px] pl-[10px] pr-[10px] text-right align-top\"\n                style={{\n                  backgroundColor: newLineNumberBG,\n                  color: `var(${hasDiff.value ? plainLineNumberColorName : expandLineNumberColorName})`,\n                  borderLeftColor: `var(${borderColorName})`,\n                  borderLeftStyle: \"solid\",\n                }}\n              >\n                {hasDiff.value && enableAddWidget.value && (\n                  <DiffSplitAddWidget\n                    index={props.index}\n                    lineNumber={newLine.value.lineNumber}\n                    side={SplitSide.new}\n                    diffFile={props.diffFile}\n                    onWidgetClick={onAddWidgetClick}\n                    className=\"absolute left-[100%] z-[1] translate-x-[-50%]\"\n                    onOpenAddWidget={onOpenAddWidget}\n                  />\n                )}\n                <span data-line-num={newLine.value.lineNumber} style={{ opacity: hasChange.value ? undefined : 0.5 }}>\n                  {newLine.value.lineNumber}\n                </span>\n              </td>\n              <td\n                class=\"diff-line-new-content group relative pr-[10px] align-top\"\n                style={{ backgroundColor: newLineContentBG }}\n                data-side={SplitSide[SplitSide.new]}\n              >\n                {hasDiff.value && enableAddWidget.value && (\n                  <DiffSplitAddWidget\n                    index={props.index}\n                    lineNumber={newLine.value.lineNumber}\n                    side={SplitSide.new}\n                    diffFile={props.diffFile}\n                    onWidgetClick={onAddWidgetClick}\n                    className=\"absolute right-[100%] z-[1] translate-x-[50%]\"\n                    onOpenAddWidget={onOpenAddWidget}\n                  />\n                )}\n                <DiffContent\n                  enableWrap={true}\n                  diffFile={props.diffFile}\n                  rawLine={newLine.value?.value || \"\"}\n                  diffLine={newLine.value?.diff}\n                  plainLine={newPlainLine.value}\n                  syntaxLine={newSyntaxLine.value}\n                  enableHighlight={enableHighlight.value}\n                />\n              </td>\n            </>\n          ) : (\n            <td\n              class=\"diff-line-new-placeholder select-none border-l-[1px]\"\n              style={{\n                backgroundColor: `var(${emptyBGName})`,\n                borderLeftColor: `var(${borderColorName})`,\n                borderLeftStyle: \"solid\",\n              }}\n              colspan={2}\n            >\n              <span>&ensp;</span>\n            </td>\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitContentLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { borderColorName, emptyBGName } from \"@git-diff-view/utils\";\nimport { computed, defineComponent } from \"vue\";\n\nimport { useExtendData, useSlots } from \"../context\";\n\nimport { SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitExtendLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const extendData = useExtendData();\n\n    const slots = useSlots();\n\n    const oldLine = computed(() => props.diffFile.getSplitLeftLine(props.index));\n\n    const newLine = computed(() => props.diffFile.getSplitRightLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const oldLineExtend = computed(() => extendData.value?.oldFile?.[oldLine.value?.lineNumber]);\n\n    const newLineExtend = computed(() => extendData.value?.newFile?.[newLine.value.lineNumber]);\n\n    const hasHidden = computed(() => oldLine.value.isHidden && newLine.value.isHidden);\n\n    const currentIsShow = computed(() =>\n      Boolean((oldLineExtend.value || newLineExtend.value) && (!hasHidden.value || enableExpand.value) && slots.extend)\n    );\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      const oldExtendRendered = oldLineExtend.value\n        ? slots.extend?.({\n            diffFile: props.diffFile,\n            side: SplitSide.old,\n            lineNumber: oldLine.value.lineNumber,\n            data: oldLineExtend.value.data,\n            onUpdate: props.diffFile.notifyAll,\n          })\n        : null;\n\n      const newExtendRendered = newLineExtend.value\n        ? slots.extend?.({\n            diffFile: props.diffFile,\n            side: SplitSide.new,\n            lineNumber: newLine.value.lineNumber,\n            data: newLineExtend.value.data,\n            onUpdate: props.diffFile.notifyAll,\n          })\n        : null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-extend`} data-state=\"extend\" class=\"diff-line diff-line-extend\">\n          {oldExtendRendered ? (\n            <td class=\"diff-line-extend-old-content p-0\" colspan={2}>\n              <div class=\"diff-line-extend-wrapper\">{oldExtendRendered}</div>\n            </td>\n          ) : (\n            <td\n              class=\"diff-line-extend-old-placeholder select-none p-0\"\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            />\n          )}\n          {newExtendRendered ? (\n            <td\n              class=\"diff-line-extend-new-content border-l-[1px] p-0\"\n              colspan={2}\n              style={{ borderLeftColor: `var(${borderColorName})`, borderLeftStyle: \"solid\" }}\n            >\n              <div class=\"diff-line-extend-wrapper\">{newExtendRendered}</div>\n            </td>\n          ) : (\n            <td\n              class=\"diff-line-extend-new-placeholder select-none border-l-[1px] p-0\"\n              style={{\n                backgroundColor: `var(${emptyBGName})`,\n                borderLeftColor: `var(${borderColorName})`,\n                borderLeftStyle: \"solid\",\n              }}\n              colspan={2}\n            />\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitExtendLine\", props: [\"index\", \"diffFile\", \"lineNumber\"] }\n);\n", "import { composeLen, type DiffFile } from \"@git-diff-view/core\";\nimport {\n  borderColorName,\n  hunkContentBGName,\n  hunkContentColorName,\n  hunkLineNumberBGName,\n  plainLineNumberColorName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { DiffModeEnum } from \"..\";\nimport { useMode } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\n\nimport { ExpandAll, ExpandDown, ExpandUp } from \"./DiffExpand\";\n\nconst DiffSplitHunkLineGitHub = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const currentHunk = computed(() => props.diffFile.getSplitHunkLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const couldExpand = computed(() => enableExpand.value && currentHunk.value && currentHunk.value.splitInfo);\n\n    const currentShowExpandAll = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen\n    );\n\n    const currentIsShow = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex\n    );\n\n    const currentIsFirstLine = computed(() => currentHunk.value && currentHunk.value.isFirst);\n\n    const currentIsLastLine = computed(() => currentHunk.value && currentHunk.value.isLast);\n\n    const currentIsPureHunk = computed(\n      () => currentHunk.value && props.diffFile._getIsPureDiffRender() && !currentHunk.value.splitInfo\n    );\n\n    useSubscribeDiffFile(props, () => {\n      currentShowExpandAll.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen;\n\n      currentIsShow.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex;\n    });\n\n    return () => {\n      if (!currentIsShow.value && !currentIsPureHunk.value) return null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-hunk`} data-state=\"hunk\" class=\"diff-line diff-line-hunk\">\n          <td\n            class=\"diff-line-hunk-action relative w-[1%] min-w-[40px] select-none p-[1px]\"\n            style={{\n              backgroundColor: `var(${hunkLineNumberBGName})`,\n              color: `var(${plainLineNumberColorName})`,\n            }}\n          >\n            {couldExpand.value ? (\n              currentIsFirstLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Up\"\n                  data-title=\"Expand Up\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                >\n                  <ExpandUp className=\"fill-current\" />\n                </button>\n              ) : currentIsLastLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Down\"\n                  data-title=\"Expand Down\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                >\n                  <ExpandDown className=\"fill-current\" />\n                </button>\n              ) : currentShowExpandAll.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand All\"\n                  data-title=\"Expand All\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"all\", props.index)}\n                >\n                  <ExpandAll className=\"fill-current\" />\n                </button>\n              ) : (\n                <>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Down\"\n                    data-title=\"Expand Down\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                  >\n                    <ExpandDown className=\"fill-current\" />\n                  </button>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Up\"\n                    data-title=\"Expand Up\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                  >\n                    <ExpandUp className=\"fill-current\" />\n                  </button>\n                </>\n              )\n            ) : (\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            )}\n          </td>\n          <td\n            class=\"diff-line-hunk-content pr-[10px] align-middle\"\n            style={{ backgroundColor: `var(${hunkContentBGName})` }}\n            colspan={3}\n          >\n            <div\n              class=\"pl-[1.5em]\"\n              style={{\n                color: `var(${hunkContentColorName})`,\n              }}\n            >\n              {currentHunk.value.splitInfo?.plainText || currentHunk.value.text}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n\nconst DiffSplitHunkLineGitLab = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const currentHunk = computed(() => props.diffFile.getSplitHunkLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const couldExpand = computed(() => enableExpand.value && currentHunk.value && currentHunk.value.splitInfo);\n\n    const currentShowExpandAll = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen\n    );\n\n    const currentIsShow = ref(\n      currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex\n    );\n\n    const currentIsFirstLine = computed(() => currentHunk.value && currentHunk.value.isFirst);\n\n    const currentIsPureHunk = computed(\n      () => currentHunk.value && props.diffFile._getIsPureDiffRender() && !currentHunk.value.splitInfo\n    );\n\n    const currentIsLastLine = computed(() => currentHunk.value && currentHunk.value.isLast);\n\n    useSubscribeDiffFile(props, () => {\n      currentShowExpandAll.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.endHiddenIndex - currentHunk.value.splitInfo.startHiddenIndex < composeLen;\n\n      currentIsShow.value =\n        currentHunk.value &&\n        currentHunk.value.splitInfo &&\n        currentHunk.value.splitInfo.startHiddenIndex < currentHunk.value.splitInfo.endHiddenIndex;\n    });\n\n    return () => {\n      if (!currentIsShow.value && !currentIsPureHunk.value) return null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-hunk`} data-state=\"hunk\" class=\"diff-line diff-line-hunk\">\n          <td\n            class=\"diff-line-hunk-action relative w-[1%] min-w-[40px] select-none p-[1px]\"\n            style={{\n              backgroundColor: `var(${hunkLineNumberBGName})`,\n              color: `var(${plainLineNumberColorName})`,\n            }}\n          >\n            {couldExpand.value ? (\n              currentIsFirstLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Up\"\n                  data-title=\"Expand Up\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                >\n                  <ExpandUp className=\"fill-current\" />\n                </button>\n              ) : currentIsLastLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Down\"\n                  data-title=\"Expand Down\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                >\n                  <ExpandDown className=\"fill-current\" />\n                </button>\n              ) : currentShowExpandAll.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand All\"\n                  data-title=\"Expand All\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"all\", props.index)}\n                >\n                  <ExpandAll className=\"fill-current\" />\n                </button>\n              ) : (\n                <>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Down\"\n                    data-title=\"Expand Down\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                  >\n                    <ExpandDown className=\"fill-current\" />\n                  </button>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Up\"\n                    data-title=\"Expand Up\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                  >\n                    <ExpandUp className=\"fill-current\" />\n                  </button>\n                </>\n              )\n            ) : (\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            )}\n          </td>\n          <td\n            class=\"diff-line-hunk-content pr-[10px] align-middle\"\n            style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          >\n            <div\n              class=\"pl-[1.5em]\"\n              style={{\n                color: `var(${hunkContentColorName})`,\n              }}\n            >\n              {currentHunk.value.splitInfo?.plainText || currentHunk.value.text}\n            </div>\n          </td>\n          <td\n            class=\"diff-line-hunk-action relative z-[1] w-[1%] min-w-[40px] select-none border-l-[1px] p-[1px]\"\n            style={{\n              backgroundColor: `var(${hunkLineNumberBGName})`,\n              color: `var(${plainLineNumberColorName})`,\n              borderLeftColor: `var(${borderColorName})`,\n              borderLeftStyle: \"solid\",\n            }}\n          >\n            {couldExpand.value ? (\n              currentIsFirstLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Up\"\n                  data-title=\"Expand Up\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                >\n                  <ExpandUp className=\"fill-current\" />\n                </button>\n              ) : currentIsLastLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Down\"\n                  data-title=\"Expand Down\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                >\n                  <ExpandDown className=\"fill-current\" />\n                </button>\n              ) : currentShowExpandAll.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand All\"\n                  data-title=\"Expand All\"\n                  onClick={() => props.diffFile.onSplitHunkExpand(\"all\", props.index)}\n                >\n                  <ExpandAll className=\"fill-current\" />\n                </button>\n              ) : (\n                <>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Down\"\n                    data-title=\"Expand Down\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"down\", props.index)}\n                  >\n                    <ExpandDown className=\"fill-current\" />\n                  </button>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Up\"\n                    data-title=\"Expand Up\"\n                    onClick={() => props.diffFile.onSplitHunkExpand(\"up\", props.index)}\n                  >\n                    <ExpandUp className=\"fill-current\" />\n                  </button>\n                </>\n              )\n            ) : (\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            )}\n          </td>\n          <td\n            class=\"diff-line-hunk-content relative pr-[10px] align-middle\"\n            style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          >\n            <div\n              class=\"pl-[1.5em]\"\n              style={{\n                color: `var(${hunkContentColorName})`,\n              }}\n            >\n              {currentHunk.value.splitInfo?.plainText || currentHunk.value.text}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n\nexport const DiffSplitHunkLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const diffViewMode = useMode();\n\n    return () => {\n      if (diffViewMode.value === DiffModeEnum.SplitGitHub || diffViewMode.value === DiffModeEnum.Split) {\n        return <DiffSplitHunkLineGitHub index={props.index} diffFile={props.diffFile} lineNumber={props.lineNumber} />;\n      } else {\n        return <DiffSplitHunkLineGitLab index={props.index} diffFile={props.diffFile} lineNumber={props.lineNumber} />;\n      }\n    };\n  },\n  { name: \"DiffSplitHunkLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { borderColorName, emptyBGName } from \"@git-diff-view/utils\";\nimport { computed, defineComponent } from \"vue\";\n\nimport { useSetWidget, useSlots, useWidget } from \"../context\";\n\nimport { SplitSide } from \"./DiffView\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitWidgetLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const slots = useSlots();\n\n    const widget = useWidget();\n\n    const setWidget = useSetWidget();\n\n    const oldLine = computed(() => props.diffFile.getSplitLeftLine(props.index));\n\n    const newLine = computed(() => props.diffFile.getSplitRightLine(props.index));\n\n    const oldLineWidget = computed(\n      () =>\n        oldLine.value.lineNumber &&\n        widget.value.side === SplitSide.old &&\n        widget.value.lineNumber === oldLine.value.lineNumber\n    );\n\n    const newLineWidget = computed(\n      () =>\n        newLine.value.lineNumber &&\n        widget.value.side === SplitSide.new &&\n        widget.value.lineNumber === newLine.value.lineNumber\n    );\n\n    const hasHidden = computed(() => oldLine.value.isHidden && newLine.value.isHidden);\n\n    const currentIsShow = computed(\n      () => (!!oldLineWidget.value || !!newLineWidget.value) && !hasHidden.value && !!slots.widget\n    );\n\n    const onCloseWidget = () => setWidget({});\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      const oldWidgetRendered = oldLineWidget.value\n        ? slots.widget?.({\n            diffFile: props.diffFile,\n            side: SplitSide.old,\n            lineNumber: oldLine.value.lineNumber,\n            onClose: onCloseWidget,\n          })\n        : null;\n\n      const newWidgetRendered = newLineWidget.value\n        ? slots.widget?.({\n            diffFile: props.diffFile,\n            side: SplitSide.new,\n            lineNumber: newLine.value.lineNumber,\n            onClose: onCloseWidget,\n          })\n        : null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-widget`} data-state=\"widget\" class=\"diff-line diff-line-widget\">\n          {oldWidgetRendered ? (\n            <td class=\"diff-line-widget-old-content p-0\" colspan={2}>\n              <div class=\"diff-line-widget-wrapper\">{oldWidgetRendered}</div>\n            </td>\n          ) : (\n            <td\n              class=\"diff-line-widget-old-placeholder select-none p-0\"\n              style={{ backgroundColor: `var(${emptyBGName})` }}\n              colspan={2}\n            />\n          )}\n          {newWidgetRendered ? (\n            <td\n              class=\"diff-line-widget-new-content border-l-[1px] p-0\"\n              colspan={2}\n              style={{ borderLeftColor: `var(${borderColorName})`, borderLeftStyle: \"solid\" }}\n            >\n              <div class=\"diff-line-widget-wrapper\">{newWidgetRendered}</div>\n            </td>\n          ) : (\n            <td\n              class=\"diff-line-widget-new-placeholder select-none border-l-[1px] p-0\"\n              style={{\n                backgroundColor: `var(${emptyBGName})`,\n                borderLeftColor: `var(${borderColorName})`,\n                borderLeftStyle: \"solid\",\n              }}\n              colspan={2}\n            />\n          )}\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffSplitWidgetLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { getSplitContentLines } from \"@git-diff-view/core\";\nimport { diffAsideWidthName, diffFontSizeName, removeAllSelection } from \"@git-diff-view/utils\";\nimport { Fragment, computed, defineComponent, ref } from \"vue\";\n\nimport { SplitSide } from \"..\";\nimport { useFontSize } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\nimport { useTextWidth } from \"../hooks/useTextWidth\";\n\nimport { DiffSplitContentLine } from \"./DiffSplitContentLineWrap\";\nimport { DiffSplitExtendLine } from \"./DiffSplitExtendLineWrap\";\nimport { DiffSplitHunkLine } from \"./DiffSplitHunkLineWrap\";\nimport { DiffSplitWidgetLine } from \"./DiffSplitWidgetLineWrap\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitViewWrap = defineComponent(\n  (props: { diffFile: DiffFile }) => {\n    const lines = ref(getSplitContentLines(props.diffFile));\n\n    const maxText = computed(() => Math.max(props.diffFile.splitLineLength, props.diffFile.fileLineLength).toString());\n\n    const styleRef = ref<HTMLStyleElement | null>(null);\n\n    const selectState = { current: undefined as SplitSide | undefined };\n\n    const onSelect = (side?: SplitSide) => {\n      const ele = styleRef.value;\n\n      if (!ele) return;\n\n      if (!side) {\n        ele.textContent = \"\";\n      } else {\n        const id = `diff-root${props.diffFile.getId()}`;\n        ele.textContent = `#${id} [data-side=\"${SplitSide[side === SplitSide.old ? SplitSide.new : SplitSide.old]}\"] {user-select: none} \\n#${id} [data-state=\"extend\"] {user-select: none} \\n#${id} [data-state=\"hunk\"] {user-select: none} \\n#${id} [data-state=\"widget\"] {user-select: none}`;\n      }\n    };\n\n    const onMouseDown = (e: MouseEvent) => {\n      let ele = e.target;\n\n      // need remove all the selection\n      if (ele && ele instanceof HTMLElement && ele.nodeName === \"BUTTON\") {\n        removeAllSelection();\n        return;\n      }\n\n      while (ele && ele instanceof HTMLElement) {\n        const state = ele.getAttribute(\"data-state\");\n        const side = ele.getAttribute(\"data-side\");\n        if (side) {\n          if (selectState.current !== SplitSide[side]) {\n            selectState.current = SplitSide[side];\n            onSelect(SplitSide[side]);\n            removeAllSelection();\n          }\n        }\n        if (state) {\n          if (state === \"extend\" || state === \"hunk\" || state === \"widget\") {\n            if (selectState.current !== undefined) {\n              selectState.current = undefined;\n              onSelect(undefined);\n              removeAllSelection();\n            }\n            return;\n          } else {\n            return;\n          }\n        }\n\n        ele = ele.parentElement;\n      }\n    };\n\n    const fontSize = useFontSize();\n\n    const font = computed(() => ({ fontSize: fontSize.value + \"px\", fontFamily: \"Menlo, Consolas, monospace\" }));\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      lines.value = getSplitContentLines(diffFile);\n    });\n\n    const width = useTextWidth({ text: maxText, font });\n\n    const computedWidth = computed(() => Math.max(40, width.value + 25));\n\n    return () => {\n      return (\n        <div class=\"split-diff-view split-diff-view-warp w-full\">\n          <div\n            class=\"diff-table-wrapper w-full\"\n            style={{\n              [diffAsideWidthName]: `${Math.round(computedWidth.value)}px`,\n              fontFamily: \"Menlo, Consolas, monospace\",\n              fontSize: `var(${diffFontSizeName})`,\n            }}\n          >\n            <style data-select-style ref={styleRef} />\n            <table class=\"diff-table w-full table-fixed border-collapse border-spacing-0\">\n              <colgroup>\n                <col class=\"diff-table-old-num-col\" width={Math.round(computedWidth.value)} />\n                <col class=\"diff-table-old-content-col\" />\n                <col class=\"diff-table-new-num-col\" width={Math.round(computedWidth.value)} />\n                <col class=\"diff-table-new-content-col\" />\n              </colgroup>\n              <thead class=\"hidden\">\n                <tr>\n                  <th scope=\"col\">old line number</th>\n                  <th scope=\"col\">old line content</th>\n                  <th scope=\"col\">new line number</th>\n                  <th scope=\"col\">new line content</th>\n                </tr>\n              </thead>\n              <tbody class=\"diff-table-body leading-[1.4]\" onMousedown={onMouseDown}>\n                {lines.value.map((item) => (\n                  <Fragment key={item.index}>\n                    <DiffSplitHunkLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffSplitContentLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffSplitWidgetLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffSplitExtendLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                  </Fragment>\n                ))}\n                <DiffSplitHunkLine\n                  index={props.diffFile.splitLineLength}\n                  lineNumber={props.diffFile.splitLineLength}\n                  diffFile={props.diffFile}\n                />\n              </tbody>\n            </table>\n          </div>\n        </div>\n      );\n    };\n  },\n  { name: \"DiffSplitViewWrap\", props: [\"diffFile\"] }\n);\n", "import { defineComponent } from \"vue\";\n\nimport { useEnableWrap } from \"../context\";\n\nimport { DiffSplitViewNormal } from \"./DiffSplitViewNormal\";\nimport { DiffSplitViewWrap } from \"./DiffSplitViewWrap\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffSplitView = defineComponent(\n  (props: { diffFile: DiffFile }) => {\n    const enableWrap = useEnableWrap();\n\n    return () => {\n      return enableWrap.value ? (\n        <DiffSplitViewWrap diffFile={props.diffFile} />\n      ) : (\n        <DiffSplitViewNormal diffFile={props.diffFile} />\n      );\n    };\n  },\n  { name: \"DiffSplitView\", props: [\"diffFile\"] }\n);\n", "import { type DiffFile, type DiffLine, checkDiffLineIncludeChange, type File } from \"@git-diff-view/core\";\nimport {\n  diffAsideWidthName,\n  addContentBGName,\n  addLineNumberBGName,\n  delContentBGName,\n  delLineNumberBGName,\n  expandContentBGName,\n  plainContentBGName,\n  plainLineNumberBGName,\n  plainLineNumberColorName,\n  expandLineNumberColorName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { SplitSide } from \"..\";\nimport { useEnableAddWidget, useEnableHighlight, useEnableWrap, useOnAddWidgetClick, useSetWidget } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\n\nimport { DiffUnifiedAddWidget } from \"./DiffAddWidget\";\nimport { DiffContent } from \"./DiffContent\";\n\nconst DiffUnifiedOldLine = ({\n  index,\n  diffLine,\n  rawLine,\n  plainLine,\n  syntaxLine,\n  lineNumber,\n  diffFile,\n  enableWrap,\n  enableAddWidget,\n  enableHighlight,\n  onOpenAddWidget,\n  onAddWidgetClick,\n}: {\n  index: number;\n  lineNumber: number;\n  rawLine: string;\n  plainLine?: File[\"plainFile\"][number];\n  syntaxLine?: File[\"syntaxFile\"][number];\n  diffLine?: DiffLine;\n  diffFile: DiffFile;\n  enableWrap: boolean;\n  enableAddWidget: boolean;\n  enableHighlight: boolean;\n  onOpenAddWidget: (lineNumber: number, side: SplitSide) => void;\n  onAddWidgetClick?: (event: \"onAddWidgetClick\", lineNumber: number, side: SplitSide) => void;\n}) => {\n  return (\n    <tr data-line={index} data-state=\"diff\" class=\"diff-line group\">\n      <td\n        class=\"diff-line-num sticky left-0 z-[1] w-[1%] min-w-[100px] select-none whitespace-nowrap pl-[10px] pr-[10px] text-right align-top\"\n        style={{\n          color: `var(${plainLineNumberColorName})`,\n          backgroundColor: `var(${delLineNumberBGName})`,\n          width: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n          maxWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n          minWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n        }}\n      >\n        {enableAddWidget && (\n          <DiffUnifiedAddWidget\n            index={index - 1}\n            lineNumber={lineNumber}\n            diffFile={diffFile}\n            side={SplitSide.old}\n            onWidgetClick={onAddWidgetClick}\n            onOpenAddWidget={onOpenAddWidget}\n          />\n        )}\n        <div class=\"flex\">\n          <span data-line-old-num={lineNumber} class=\"inline-block w-[50%]\">\n            {lineNumber}\n          </span>\n          <span class=\"w-[10px] shrink-0\" />\n          <span class=\"inline-block w-[50%]\" />\n        </div>\n      </td>\n      <td class=\"diff-line-content pr-[10px] align-top\" style={{ backgroundColor: `var(${delContentBGName})` }}>\n        <DiffContent\n          enableWrap={enableWrap}\n          diffFile={diffFile}\n          enableHighlight={enableHighlight}\n          rawLine={rawLine}\n          diffLine={diffLine}\n          plainLine={plainLine}\n          syntaxLine={syntaxLine}\n        />\n      </td>\n    </tr>\n  );\n};\n\nconst DiffUnifiedNewLine = ({\n  index,\n  diffLine,\n  rawLine,\n  plainLine,\n  syntaxLine,\n  lineNumber,\n  diffFile,\n  enableWrap,\n  enableAddWidget,\n  enableHighlight,\n  onOpenAddWidget,\n  onAddWidgetClick,\n}: {\n  index: number;\n  lineNumber: number;\n  rawLine: string;\n  plainLine?: File[\"plainFile\"][number];\n  syntaxLine?: File[\"syntaxFile\"][number];\n  diffLine?: DiffLine;\n  diffFile: DiffFile;\n  enableWrap: boolean;\n  enableAddWidget: boolean;\n  enableHighlight: boolean;\n  onOpenAddWidget: (lineNumber: number, side: SplitSide) => void;\n  onAddWidgetClick?: (event: \"onAddWidgetClick\", lineNumber: number, side: SplitSide) => void;\n}) => {\n  return (\n    <tr data-line={index} data-state=\"diff\" class=\"diff-line group\">\n      <td\n        class=\"diff-line-num sticky left-0 z-[1] w-[1%] min-w-[100px] select-none whitespace-nowrap pl-[10px] pr-[10px] text-right align-top\"\n        style={{\n          color: `var(${plainLineNumberColorName})`,\n          backgroundColor: `var(${addLineNumberBGName})`,\n          width: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n          maxWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n          minWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n        }}\n      >\n        {enableAddWidget && (\n          <DiffUnifiedAddWidget\n            index={index - 1}\n            lineNumber={lineNumber}\n            diffFile={diffFile}\n            side={SplitSide.new}\n            onWidgetClick={onAddWidgetClick}\n            onOpenAddWidget={onOpenAddWidget}\n          />\n        )}\n        <div class=\"flex\">\n          <span class=\"inline-block w-[50%]\" />\n          <span class=\"w-[10px] shrink-0\" />\n          <span data-line-new-num={lineNumber} class=\"inline-block w-[50%]\">\n            {lineNumber}\n          </span>\n        </div>\n      </td>\n      <td class=\"diff-line-content pr-[10px] align-top\" style={{ backgroundColor: `var(${addContentBGName})` }}>\n        <DiffContent\n          enableWrap={enableWrap}\n          diffFile={diffFile}\n          enableHighlight={enableHighlight}\n          rawLine={rawLine}\n          diffLine={diffLine}\n          plainLine={plainLine}\n          syntaxLine={syntaxLine}\n        />\n      </td>\n    </tr>\n  );\n};\n\nexport const DiffUnifiedContentLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const unifiedItem = computed(() => props.diffFile.getUnifiedLine(props.index));\n\n    const enableWrap = useEnableWrap();\n\n    const setWidget = useSetWidget();\n\n    const onAddWidgetClick = useOnAddWidgetClick();\n\n    const enableHighlight = useEnableHighlight();\n\n    const enableAddWidget = useEnableAddWidget();\n\n    const currentItemHasHidden = computed(() => unifiedItem.value?.isHidden);\n\n    const currentItemHasChange = computed(() => checkDiffLineIncludeChange(unifiedItem.value?.diff));\n\n    const currentSyntaxLine = ref(\n      unifiedItem.value?.newLineNumber\n        ? props.diffFile.getNewSyntaxLine(unifiedItem.value.newLineNumber)\n        : unifiedItem.value?.oldLineNumber\n          ? props.diffFile.getOldSyntaxLine(unifiedItem.value.oldLineNumber)\n          : undefined\n    );\n\n    const currentPlainLine = ref(\n      unifiedItem.value?.newLineNumber\n        ? props.diffFile.getNewPlainLine(unifiedItem.value.newLineNumber)\n        : unifiedItem.value?.oldLineNumber\n          ? props.diffFile.getOldPlainLine(unifiedItem.value.oldLineNumber)\n          : undefined\n    );\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      currentSyntaxLine.value = unifiedItem.value?.newLineNumber\n        ? diffFile.getNewSyntaxLine(unifiedItem.value.newLineNumber)\n        : unifiedItem.value?.oldLineNumber\n          ? diffFile.getOldSyntaxLine(unifiedItem.value.oldLineNumber)\n          : undefined;\n\n      currentPlainLine.value = unifiedItem.value?.newLineNumber\n        ? diffFile.getNewPlainLine(unifiedItem.value.newLineNumber)\n        : unifiedItem.value?.oldLineNumber\n          ? diffFile.getOldPlainLine(unifiedItem.value.oldLineNumber)\n          : undefined;\n    });\n\n    const onOpenAddWidget = (lineNumber: number, side: SplitSide) => setWidget({ side: side, lineNumber: lineNumber });\n\n    return () => {\n      if (currentItemHasHidden.value) return null;\n\n      if (currentItemHasChange.value) {\n        if (unifiedItem.value.oldLineNumber) {\n          return (\n            <DiffUnifiedOldLine\n              index={props.lineNumber}\n              enableWrap={enableWrap.value}\n              diffFile={props.diffFile}\n              rawLine={unifiedItem.value.value || \"\"}\n              diffLine={unifiedItem.value.diff}\n              plainLine={currentPlainLine.value}\n              syntaxLine={currentSyntaxLine.value}\n              enableHighlight={enableHighlight.value}\n              enableAddWidget={enableAddWidget.value}\n              lineNumber={unifiedItem.value.oldLineNumber}\n              onAddWidgetClick={onAddWidgetClick}\n              onOpenAddWidget={onOpenAddWidget}\n            />\n          );\n        } else {\n          return (\n            <DiffUnifiedNewLine\n              index={props.lineNumber}\n              enableWrap={enableWrap.value}\n              diffFile={props.diffFile}\n              rawLine={unifiedItem.value.value || \"\"}\n              diffLine={unifiedItem.value.diff}\n              plainLine={currentPlainLine.value}\n              syntaxLine={currentSyntaxLine.value}\n              enableHighlight={enableHighlight.value}\n              enableAddWidget={enableAddWidget.value}\n              lineNumber={unifiedItem.value.newLineNumber!}\n              onAddWidgetClick={onAddWidgetClick}\n              onOpenAddWidget={onOpenAddWidget}\n            />\n          );\n        }\n      } else {\n        return (\n          <tr\n            data-line={props.lineNumber}\n            data-state={unifiedItem.value.diff ? \"diff\" : \"plain\"}\n            class=\"diff-line group\"\n          >\n            <td\n              class=\"diff-line-num sticky left-0 z-[1] w-[1%] min-w-[100px] select-none whitespace-nowrap pl-[10px] pr-[10px] text-right align-top\"\n              style={{\n                color: `var(${unifiedItem.value.diff ? plainLineNumberColorName : expandLineNumberColorName})`,\n                backgroundColor: unifiedItem.value.diff\n                  ? `var(${plainLineNumberBGName})`\n                  : `var(${expandContentBGName})`,\n                width: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n                maxWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n                minWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n              }}\n            >\n              {enableAddWidget.value && unifiedItem.value.diff && (\n                <DiffUnifiedAddWidget\n                  index={props.index}\n                  diffFile={props.diffFile}\n                  lineNumber={unifiedItem.value.newLineNumber}\n                  side={SplitSide.new}\n                  onOpenAddWidget={onOpenAddWidget}\n                  onWidgetClick={onAddWidgetClick}\n                />\n              )}\n              <div class=\"flex opacity-[0.5]\">\n                <span data-line-old-num={unifiedItem.value.oldLineNumber} class=\"inline-block w-[50%]\">\n                  {unifiedItem.value.oldLineNumber}\n                </span>\n                <span class=\"w-[10px] shrink-0\" />\n                <span data-line-new-num={unifiedItem.value.newLineNumber} class=\"inline-block w-[50%]\">\n                  {unifiedItem.value.newLineNumber}\n                </span>\n              </div>\n            </td>\n            <td\n              class=\"diff-line-content pr-[10px] align-top\"\n              style={{\n                backgroundColor: unifiedItem.value.diff ? `var(${plainContentBGName})` : `var(${expandContentBGName})`,\n              }}\n            >\n              <DiffContent\n                enableWrap={enableWrap.value}\n                diffFile={props.diffFile}\n                enableHighlight={enableHighlight.value}\n                rawLine={unifiedItem.value.value || \"\"}\n                diffLine={unifiedItem.value.diff}\n                plainLine={currentPlainLine.value}\n                syntaxLine={currentSyntaxLine.value}\n              />\n            </td>\n          </tr>\n        );\n      }\n    };\n  },\n  { name: \"DiffUnifiedContentLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { computed, defineComponent, ref } from \"vue\";\n\nimport { SplitSide } from \"..\";\nimport { useExtendData, useSlots } from \"../context\";\nimport { useDomWidth } from \"../hooks/useDomWidth\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffUnifiedExtendLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const extendData = useExtendData();\n\n    const slots = useSlots();\n\n    const unifiedItem = computed(() => props.diffFile.getUnifiedLine(props.index));\n\n    const oldExtend = computed(() => extendData.value?.oldFile?.[unifiedItem.value.oldLineNumber]);\n\n    const newExtend = computed(() => extendData.value?.newFile?.[unifiedItem.value.newLineNumber]);\n\n    const currentIsHidden = computed(() => unifiedItem.value.isHidden);\n\n    const currentIsShow = computed(() =>\n      Boolean((oldExtend.value || newExtend.value) && !currentIsHidden.value && slots.extend)\n    );\n\n    const width = useDomWidth({\n      selector: ref(\".unified-diff-table-wrapper\"),\n      enable: currentIsShow,\n    });\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-extend`} data-state=\"extend\" class=\"diff-line diff-line-extend\">\n          <td class=\"diff-line-extend-content p-0 align-top\" colspan={2}>\n            <div class=\"diff-line-extend-wrapper sticky left-0 z-[1]\" style={{ width: width.value + \"px\" }}>\n              {width.value > 0 &&\n                oldExtend.value &&\n                slots.extend({\n                  diffFile: props.diffFile,\n                  side: SplitSide.old,\n                  lineNumber: unifiedItem.value.oldLineNumber,\n                  data: oldExtend.value.data,\n                  onUpdate: props.diffFile.notifyAll,\n                })}\n              {width.value > 0 &&\n                newExtend.value &&\n                slots.extend({\n                  diffFile: props.diffFile,\n                  side: SplitSide.new,\n                  lineNumber: unifiedItem.value.newLineNumber,\n                  data: newExtend.value.data,\n                  onUpdate: props.diffFile.notifyAll,\n                })}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffUnifiedExtendLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { composeLen } from \"@git-diff-view/core\";\nimport {\n  hunkContentBGName,\n  hunkContentColorName,\n  hunkLineNumberBGName,\n  plainLineNumberColorName,\n  diffAsideWidthName,\n} from \"@git-diff-view/utils\";\nimport { computed, defineComponent, ref } from \"vue\";\n\nimport { useEnableWrap } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\n\nimport { ExpandAll, ExpandDown, ExpandUp } from \"./DiffExpand\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffUnifiedHunkLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const currentHunk = computed(() => props.diffFile.getUnifiedHunkLine(props.index));\n\n    const enableExpand = computed(() => props.diffFile.getExpandEnabled());\n\n    const couldExpand = computed(() => enableExpand.value && currentHunk.value && currentHunk.value.unifiedInfo);\n\n    const enableWrap = useEnableWrap();\n\n    const currentIsShow = ref(\n      currentHunk.value &&\n        currentHunk.value.unifiedInfo &&\n        currentHunk.value.unifiedInfo.startHiddenIndex < currentHunk.value.unifiedInfo.endHiddenIndex\n    );\n\n    const currentIsEnableAll = ref(\n      currentHunk.value &&\n        currentHunk.value.unifiedInfo &&\n        currentHunk.value.unifiedInfo.endHiddenIndex - currentHunk.value.unifiedInfo.startHiddenIndex < composeLen\n    );\n\n    const currentIsFirstLine = computed(() => currentHunk.value && currentHunk.value.isFirst);\n\n    const currentIsLastLine = computed(() => currentHunk.value && currentHunk.value.isLast);\n\n    const currentIsPureHunk = computed(\n      () => currentHunk.value && props.diffFile._getIsPureDiffRender() && !currentHunk.value.unifiedInfo\n    );\n\n    useSubscribeDiffFile(props, () => {\n      currentIsShow.value =\n        currentHunk.value &&\n        currentHunk.value.unifiedInfo &&\n        currentHunk.value.unifiedInfo.startHiddenIndex < currentHunk.value.unifiedInfo.endHiddenIndex;\n\n      currentIsEnableAll.value =\n        currentHunk.value &&\n        currentHunk.value.unifiedInfo &&\n        currentHunk.value.unifiedInfo.endHiddenIndex - currentHunk.value.unifiedInfo.startHiddenIndex < composeLen;\n    });\n\n    return () => {\n      if (!currentIsShow.value && !currentIsPureHunk.value) return null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-hunk`} data-state=\"hunk\" class=\"diff-line diff-line-hunk\">\n          <td\n            class=\"diff-line-hunk-action sticky left-0 w-[1%] min-w-[100px] select-none\"\n            style={{\n              backgroundColor: `var(${hunkLineNumberBGName})`,\n              color: `var(${plainLineNumberColorName})`,\n              width: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n              maxWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n              minWidth: `calc(calc(var(${diffAsideWidthName}) + 5px) * 2)`,\n            }}\n          >\n            {couldExpand.value ? (\n              currentIsFirstLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Up\"\n                  data-title=\"Expand Up\"\n                  onClick={() => props.diffFile.onUnifiedHunkExpand(\"up\", props.index)}\n                >\n                  <ExpandUp className=\"fill-current\" />\n                </button>\n              ) : currentIsLastLine.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand Down\"\n                  data-title=\"Expand Down\"\n                  onClick={() => props.diffFile.onUnifiedHunkExpand(\"down\", props.index)}\n                >\n                  <ExpandDown className=\"fill-current\" />\n                </button>\n              ) : currentIsEnableAll.value ? (\n                <button\n                  class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[6px]\"\n                  title=\"Expand All\"\n                  data-title=\"Expand All\"\n                  onClick={() => props.diffFile.onUnifiedHunkExpand(\"all\", props.index)}\n                >\n                  <ExpandAll className=\"fill-current\" />\n                </button>\n              ) : (\n                <>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Down\"\n                    data-title=\"Expand Down\"\n                    onClick={() => props.diffFile.onUnifiedHunkExpand(\"down\", props.index)}\n                  >\n                    <ExpandDown className=\"fill-current\" />\n                  </button>\n                  <button\n                    class=\"diff-widget-tooltip flex w-full cursor-pointer items-center justify-center rounded-[2px] py-[2px]\"\n                    title=\"Expand Up\"\n                    data-title=\"Expand Up\"\n                    onClick={() => props.diffFile.onUnifiedHunkExpand(\"up\", props.index)}\n                  >\n                    <ExpandUp className=\"fill-current\" />\n                  </button>\n                </>\n              )\n            ) : (\n              <div class=\"min-h-[28px]\">&ensp;</div>\n            )}\n          </td>\n          <td\n            class=\"diff-line-hunk-content pr-[10px] align-middle\"\n            style={{ backgroundColor: `var(${hunkContentBGName})` }}\n          >\n            <div\n              class=\"pl-[1.5em]\"\n              style={{\n                whiteSpace: enableWrap.value ? \"pre-wrap\" : \"pre\",\n                wordBreak: enableWrap.value ? \"break-all\" : \"initial\",\n                color: `var(${hunkContentColorName})`,\n              }}\n            >\n              {currentHunk.value.unifiedInfo?.plainText || currentHunk.value.text}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffUnifiedHunkLine\", props: [\"index\", \"diffFile\", \"lineNumber\"] }\n);\n", "import { computed, defineComponent, ref } from \"vue\";\n\nimport { SplitSide } from \"..\";\nimport { useSetWidget, useSlots, useWidget } from \"../context\";\nimport { useDomWidth } from \"../hooks/useDomWidth\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffUnifiedWidgetLine = defineComponent(\n  (props: { index: number; diffFile: DiffFile; lineNumber: number }) => {\n    const slots = useSlots();\n\n    const widget = useWidget();\n\n    const setWidget = useSetWidget();\n\n    const unifiedItem = computed(() => props.diffFile.getUnifiedLine(props.index));\n\n    const oldWidget = computed(\n      () =>\n        unifiedItem.value?.oldLineNumber &&\n        widget.value.side === SplitSide.old &&\n        widget.value.lineNumber === unifiedItem.value.oldLineNumber\n    );\n\n    const newWidget = computed(\n      () =>\n        unifiedItem.value?.newLineNumber &&\n        widget.value.side === SplitSide.new &&\n        widget.value.lineNumber === unifiedItem.value.newLineNumber\n    );\n\n    const currentIsHidden = computed(() => unifiedItem.value.isHidden);\n\n    const currentIsShow = computed(\n      () => (oldWidget.value || newWidget.value) && !currentIsHidden.value && !!slots.widget\n    );\n\n    const onCloseWidget = () => setWidget({});\n\n    const width = useDomWidth({\n      selector: ref(\".unified-diff-table-wrapper\"),\n      enable: currentIsShow,\n    });\n\n    return () => {\n      if (!currentIsShow.value) return null;\n\n      return (\n        <tr data-line={`${props.lineNumber}-widget`} data-state=\"widget\" class=\"diff-line diff-line-widget\">\n          <td class=\"diff-line-widget-content p-0\" colspan={2}>\n            <div class=\"diff-line-widget-wrapper sticky left-0 z-[1]\" style={{ width: width.value + \"px\" }}>\n              {width.value > 0 &&\n                oldWidget.value &&\n                slots.widget?.({\n                  diffFile: props.diffFile,\n                  side: SplitSide.old,\n                  lineNumber: unifiedItem.value.oldLineNumber,\n                  onClose: onCloseWidget,\n                })}\n              {width.value > 0 &&\n                newWidget.value &&\n                slots.widget?.({\n                  diffFile: props.diffFile,\n                  side: SplitSide.new,\n                  lineNumber: unifiedItem.value.newLineNumber,\n                  onClose: onCloseWidget,\n                })}\n            </div>\n          </td>\n        </tr>\n      );\n    };\n  },\n  { name: \"DiffUnifiedWidgetLine\", props: [\"diffFile\", \"index\", \"lineNumber\"] }\n);\n", "import { getUnifiedContentLine } from \"@git-diff-view/core\";\nimport { diffAsideWidthName, diffFontSizeName, removeAllSelection } from \"@git-diff-view/utils\";\nimport { Fragment, computed, defineComponent, ref } from \"vue\";\n\nimport { useEnableWrap, useFontSize } from \"../context\";\nimport { useSubscribeDiffFile } from \"../hooks/useSubscribeDiffFile\";\nimport { useTextWidth } from \"../hooks/useTextWidth\";\n\nimport { DiffUnifiedContentLine } from \"./DiffUnifiedContentLine\";\nimport { DiffUnifiedExtendLine } from \"./DiffUnifiedExtendLine\";\nimport { DiffUnifiedHunkLine } from \"./DiffUnifiedHunkLine\";\nimport { DiffUnifiedWidgetLine } from \"./DiffUnifiedWidgetLine\";\n\nimport type { DiffFile } from \"@git-diff-view/core\";\n\nexport const DiffUnifiedView = defineComponent(\n  (props: { diffFile: DiffFile }) => {\n    const lines = ref(getUnifiedContentLine(props.diffFile));\n\n    const maxText = computed(() =>\n      Math.max(props.diffFile.unifiedLineLength, props.diffFile.fileLineLength).toString()\n    );\n\n    useSubscribeDiffFile(props, (diffFile) => {\n      lines.value = getUnifiedContentLine(diffFile);\n    });\n\n    const fontSize = useFontSize();\n\n    const enableWrap = useEnableWrap();\n\n    const selectState = { current: undefined as boolean | undefined };\n\n    const styleRef = ref<HTMLStyleElement | null>(null);\n\n    const onSelect = (state?: boolean) => {\n      const ele = styleRef.value;\n\n      if (!ele) return;\n\n      if (state === undefined) {\n        ele.textContent = \"\";\n      } else {\n        const id = `diff-root${props.diffFile.getId()}`;\n        ele.textContent = `#${id} [data-state=\"extend\"] {user-select: none} \\n#${id} [data-state=\"hunk\"] {user-select: none} \\n#${id} [data-state=\"widget\"] {user-select: none}`;\n      }\n    };\n\n    const onMouseDown = (e: MouseEvent) => {\n      let ele = e.target as HTMLElement;\n\n      if (ele && ele?.nodeName === \"BUTTON\") {\n        removeAllSelection();\n        return;\n      }\n\n      while (ele && ele instanceof HTMLElement) {\n        const state = ele.getAttribute(\"data-state\");\n        if (state) {\n          if (state === \"extend\" || state === \"hunk\" || state === \"widget\") {\n            if (selectState.current !== false) {\n              selectState.current = false;\n              onSelect(false);\n              removeAllSelection();\n            }\n          } else {\n            if (selectState.current !== true) {\n              selectState.current = true;\n              onSelect(true);\n              removeAllSelection();\n            }\n          }\n          return;\n        }\n        ele = ele.parentElement;\n      }\n    };\n\n    const font = computed(() => ({ fontSize: fontSize.value + \"px\", fontFamily: \"Menlo, Consolas, monospace\" }));\n\n    const width = useTextWidth({ text: maxText, font });\n\n    const computedWidth = computed(() => Math.max(40, width.value + 10));\n\n    return () => {\n      return (\n        <div\n          class={`unified-diff-view ${enableWrap.value ? \"unified-diff-view-wrap\" : \"unified-diff-view-normal\"} w-full`}\n        >\n          <style data-select-style ref={styleRef} />\n          <div\n            class=\"unified-diff-table-wrapper diff-table-scroll-container w-full overflow-x-auto overflow-y-hidden\"\n            style={{\n              [diffAsideWidthName]: `${Math.round(computedWidth.value)}px`,\n              fontFamily: \"Menlo, Consolas, monospace\",\n              fontSize: `var(${diffFontSizeName})`,\n            }}\n          >\n            <table\n              class={`unified-diff-table w-full border-collapse border-spacing-0 ${enableWrap.value ? \"table-fixed\" : \"\"}`}\n            >\n              <colgroup>\n                <col class=\"unified-diff-table-num-col\" />\n                <col class=\"unified-diff-table-content-col\" />\n              </colgroup>\n              <thead class=\"hidden\">\n                <tr>\n                  <th scope=\"col\">line number</th>\n                  <th scope=\"col\">line content</th>\n                </tr>\n              </thead>\n              <tbody class=\"diff-table-body leading-[1.4]\" onMousedown={onMouseDown}>\n                {lines.value.map((item) => (\n                  <Fragment key={item.index}>\n                    <DiffUnifiedHunkLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffUnifiedContentLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffUnifiedWidgetLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                    <DiffUnifiedExtendLine index={item.index} lineNumber={item.lineNumber} diffFile={props.diffFile} />\n                  </Fragment>\n                ))}\n                <DiffUnifiedHunkLine\n                  index={props.diffFile.unifiedLineLength}\n                  lineNumber={props.diffFile.unifiedLineLength}\n                  diffFile={props.diffFile}\n                />\n              </tbody>\n            </table>\n          </div>\n        </div>\n      );\n    };\n  },\n  { props: [\"diffFile\"], name: \"DiffUnifiedView\" }\n);\n", "import { DiffFile, _cacheMap, SplitSide } from \"@git-diff-view/core\";\nimport { diffFontSizeName, DiffModeEnum } from \"@git-diff-view/utils\";\nimport { defineComponent, provide, ref, watch, watchEffect, computed, onUnmounted } from \"vue\";\n\nimport {\n  idSymbol,\n  modeSymbol,\n  fontSizeSymbol,\n  enableWrapSymbol,\n  enableHighlightSymbol,\n  enableAddWidgetSymbol,\n  extendDataSymbol,\n  slotsSymbol,\n  onAddWidgetClickSymbol,\n  widgetStateSymbol,\n  setWidgetStateSymbol,\n  mountedSymbol,\n} from \"../context\";\nimport { useIsMounted } from \"../hooks/useIsMounted\";\nimport { useProvide } from \"../hooks/useProvide\";\n\nimport { DiffSplitView } from \"./DiffSplitView\";\nimport { DiffUnifiedView } from \"./DiffUnifiedView\";\n\nimport type { DiffHighlighter } from \"@git-diff-view/core\";\nimport type { CSSProperties, SlotsType } from \"vue\";\n\n_cacheMap.name = \"@git-diff-view/vue\";\n\nexport { SplitSide, DiffModeEnum };\n\nexport type DiffViewProps<T> = {\n  data?: {\n    oldFile?: { fileName?: string | null; fileLang?: string | null; content?: string | null };\n    newFile?: { fileName?: string | null; fileLang?: string | null; content?: string | null };\n    hunks: string[];\n  };\n  extendData?: { oldFile?: Record<string, { data: T }>; newFile?: Record<string, { data: T }> };\n  initialWidgetState?: { side: SplitSide; lineNumber: number };\n  diffFile?: DiffFile;\n  class?: string;\n  style?: CSSProperties;\n  registerHighlighter?: Omit<DiffHighlighter, \"getHighlighterEngine\">;\n  diffViewMode?: DiffModeEnum;\n  diffViewWrap?: boolean;\n  diffViewTheme?: \"light\" | \"dark\";\n  diffViewFontSize?: number;\n  diffViewHighlight?: boolean;\n  diffViewAddWidget?: boolean;\n};\n\ntype typeSlots = SlotsType<{\n  widget: { lineNumber: number; side: SplitSide; diffFile: DiffFile; onClose: () => void };\n  extend: { lineNumber: number; side: SplitSide; data: any; diffFile: DiffFile; onUpdate: () => void };\n}>;\n\n// vue 组件打包目前无法支持范型 也不支持 slots\nexport const DiffView = defineComponent<\n  DiffViewProps<any>,\n  { onAddWidgetClick: (lineNumber: number, side: SplitSide) => void },\n  \"onAddWidgetClick\",\n  typeSlots\n>(\n  (props, options) => {\n    const getInstance = () => {\n      if (props.diffFile) {\n        const diffFile = DiffFile.createInstance({});\n        diffFile._mergeFullBundle(props.diffFile._getFullBundle());\n        return diffFile;\n      }\n      if (props.data)\n        return new DiffFile(\n          props.data.oldFile?.fileName || \"\",\n          props.data.oldFile?.content || \"\",\n          props.data.newFile?.fileName || \"\",\n          props.data.newFile?.content || \"\",\n          props.data.hunks || [],\n          props.data.oldFile?.fileLang || \"\",\n          props.data.newFile?.fileLang || \"\"\n        );\n      return null;\n    };\n\n    const diffFile = ref<DiffFile>(getInstance());\n\n    const id = ref(diffFile.value?.getId?.());\n\n    const widgetState = ref<{ side?: SplitSide; lineNumber?: number }>({});\n\n    const wrapperRef = ref<HTMLDivElement>();\n\n    const setWidget = (v: { side?: SplitSide; lineNumber?: number }) => {\n      if (typeof options.slots.widget === \"function\") {\n        widgetState.value = v;\n      }\n    };\n\n    const enableHighlight = computed(() => props.diffViewHighlight ?? true);\n\n    const theme = computed(() => props.diffViewTheme);\n\n    watch(\n      () => props.diffFile,\n      () => {\n        diffFile.value?.clear?.();\n        diffFile.value = getInstance();\n      },\n      { immediate: true }\n    );\n\n    watch(\n      () => props.initialWidgetState,\n      () => {\n        if (props.initialWidgetState) {\n          widgetState.value = {\n            side: props.initialWidgetState.side,\n            lineNumber: props.initialWidgetState.lineNumber,\n          };\n        }\n      },\n      { immediate: true, deep: true }\n    );\n\n    watch(\n      () => props.data,\n      () => {\n        diffFile.value?.clear?.();\n        diffFile.value = getInstance();\n      },\n      { immediate: true, deep: true }\n    );\n\n    watch(\n      () => diffFile.value,\n      () => (widgetState.value = {})\n    );\n\n    const isMounted = useIsMounted();\n\n    const initSubscribe = (onClean: (cb: () => void) => void) => {\n      if (!isMounted.value || !diffFile.value || !props.diffFile) return;\n      const instance = diffFile.value as DiffFile;\n      props.diffFile._addClonedInstance(instance);\n      onClean(() => props.diffFile._delClonedInstance(instance));\n    };\n\n    const initDiff = () => {\n      if (!isMounted.value || !diffFile.value) return;\n      const instance = diffFile.value;\n      instance.initTheme(theme.value);\n      instance.initRaw();\n      instance.buildSplitDiffLines();\n      instance.buildUnifiedDiffLines();\n    };\n\n    const initSyntax = () => {\n      if (!isMounted.value || !enableHighlight.value || !diffFile.value) return;\n\n      const instance = diffFile.value;\n      instance.initSyntax({\n        registerHighlighter: props.registerHighlighter,\n      });\n      instance.notifyAll();\n    };\n\n    const initAttribute = (onClean: (cb: () => void) => void) => {\n      if (!isMounted.value || !diffFile.value || !wrapperRef.value) return;\n      const instance = diffFile.value;\n      // hack to track the value change\n      // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n      theme.value;\n      const init = () => {\n        wrapperRef.value?.setAttribute(\"data-theme\", instance._getTheme() || \"light\");\n        wrapperRef.value?.setAttribute(\"data-highlighter\", instance._getHighlighterName());\n      };\n\n      init();\n\n      const cb = instance.subscribe(init);\n\n      onClean(() => cb());\n    };\n\n    const initId = (onClean: (cb: () => void) => void) => {\n      if (!diffFile.value) return;\n      const instance = diffFile.value;\n      id.value = instance.getId();\n      onClean(() => instance.clearId());\n    };\n\n    watchEffect((onClean) => initSubscribe(onClean));\n\n    watchEffect(() => initDiff());\n\n    watchEffect(() => initSyntax());\n\n    watchEffect((onClean) => initId(onClean));\n\n    watchEffect((onClean) => initAttribute(onClean));\n\n    provide(idSymbol, id);\n\n    provide(mountedSymbol, isMounted);\n\n    provide(slotsSymbol, options.slots);\n\n    provide(onAddWidgetClickSymbol, options.emit);\n\n    provide(widgetStateSymbol, widgetState);\n\n    provide(setWidgetStateSymbol, setWidget);\n\n    useProvide(props, \"diffViewMode\", modeSymbol, { defaultValue: DiffModeEnum.SplitGitHub });\n\n    useProvide(props, \"diffViewFontSize\", fontSizeSymbol, { defaultValue: 14 });\n\n    useProvide(props, \"diffViewWrap\", enableWrapSymbol);\n\n    useProvide(props, \"diffViewHighlight\", enableHighlightSymbol);\n\n    useProvide(props, \"diffViewAddWidget\", enableAddWidgetSymbol);\n\n    useProvide(props, \"extendData\", extendDataSymbol, { deepWatch: true });\n\n    onUnmounted(() => diffFile.value?.clear?.());\n\n    options.expose({ getDiffFileInstance: () => diffFile.value });\n\n    return () => {\n      if (!diffFile.value) return null;\n\n      return (\n        <div\n          class=\"diff-tailwindcss-wrapper\"\n          data-component=\"git-diff-view\"\n          data-theme={diffFile.value._getTheme() || \"light\"}\n          data-version={__VERSION__}\n          data-highlighter={diffFile.value._getHighlighterName()}\n          ref={wrapperRef}\n        >\n          <div class=\"diff-style-root\" style={{ [diffFontSizeName]: (props.diffViewFontSize || 14) + \"px\" }}>\n            <div\n              // fix ssr mismatch error\n              id={isMounted.value ? `diff-root${id.value}` : undefined}\n              class={\"diff-view-wrapper\" + (props.class ? ` ${props.class}` : \"\")}\n              style={props.style}\n            >\n              {!props.diffViewMode || props.diffViewMode & DiffModeEnum.Split ? (\n                <DiffSplitView key={DiffModeEnum.Split} diffFile={diffFile.value as DiffFile} />\n              ) : (\n                <DiffUnifiedView key={DiffModeEnum.Unified} diffFile={diffFile.value as DiffFile} />\n              )}\n            </div>\n          </div>\n        </div>\n      );\n    };\n  },\n  {\n    name: \"DiffView\",\n    props: [\n      \"data\",\n      \"class\",\n      \"diffFile\",\n      \"diffViewAddWidget\",\n      \"diffViewFontSize\",\n      \"diffViewHighlight\",\n      \"diffViewMode\",\n      \"diffViewWrap\",\n      \"diffViewTheme\",\n      \"extendData\",\n      \"registerHighlighter\",\n      \"initialWidgetState\",\n      \"style\",\n    ],\n    // expose: [\"getDiffView\"],\n    slots: Object as typeSlots,\n  }\n);\n\nexport const version = __VERSION__;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AACtD,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;AAEA,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AAG7D,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,OAAK,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAuE,MAAM,IAAI,UAAU,KAAK,GAAI;AACxG;AAOA,IAAI;AAAJ,IAA4B;AAA5B,IAA8C;AAA9C,IAAgE;AAChE,IAAI,YAAY;AAChB,IAAM,SAAS,CAAC,MAAM,SAAS;AAC3B,SAAO,GAAG,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,IAAI;AACxE;AACA,IAAM,eAAe,CAAC,MAAM,SAAS;AACjC,SAAO,OAAO,MAAM,IAAI,OAAO,KAAK,MAAM,CAAC;AAC/C;AACA,IAAM,cAAN,MAAkB;EACd,cAAc;AACV,2BAAuB,IAAI,IAAI;AAC/B,qBAAiB,IAAI,MAAM,EAAE;AAC7B,qBAAiB,IAAI,MAAM,CAAA,CAAE;EACrC;EACI,QAAQ,MAAM,MAAM;AAChB,UAAM,aAAa,aAAa,MAAM,IAAI;AAC1C,QAAI,uBAAuB,MAAM,kBAAkB,GAAG,EAAE,UAAU,GAAG;AACjE,aAAO,uBAAuB,MAAM,kBAAkB,GAAG,EAAE,UAAU;IACjF;AACQ,UAAMA,YAAW,uBAAuB,MAAM,wBAAwB,KAAK,wBAAwB,EAAE,KAAK,IAAI;AAC9G,QAAI,MAAM;AACN,YAAM,iBAAiB,GAAG,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ;AAC5E,UAAI,uBAAuB,MAAM,kBAAkB,GAAG,MAAM,gBAAgB;AACxE,+BAAuB,MAAM,kBAAkB,cAAmB;AAClEA,kBAAS,OAAO,GAAG,KAAK,aAAa,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK,cAAc,EAAE;MACvG;IACA,OACa;AACDA,gBAAS,OAAO;IAC5B;AACQ,UAAM,YAAYA,UAAS,YAAY,IAAI,EAAE;AAC7C,WAAO;EACf;AACA;AACA,mBAAmB,oBAAI,QAAO,GAAI,mBAAmB,oBAAI,QAAO,GAAI,yBAAyB,oBAAI,QAAO,GAAI,2BAA2B,SAASC,4BAA2B;AACvK,cAAY,aAAa,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AACzE,SAAO;AACX;AACA,IAAI,WAAW;AACf,IAAM,yBAAyB,MAAM;AACjC,aAAW,YAAY,IAAI,YAAa;AACxC,SAAO;AACX;AAEA,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAClC,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAE7B,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAClC,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,eAAe,CAAC,SAAS,UAAU,YAAY;AACjD,SAAO,UACD,OAAO,gBAAgB,MACvB,WACI,OAAO,gBAAgB,MACvB,UACI,OAAO,kBAAkB,MACzB,OAAO,mBAAmB;AAC5C;AACA,IAAM,kBAAkB,CAAC,SAAS,UAAU,YAAY;AACpD,SAAO,UACD,OAAO,mBAAmB,MAC1B,WACI,OAAO,mBAAmB,MAC1B,UACI,OAAO,qBAAqB,MAC5B,OAAO,sBAAsB;AAC/C;AAEA,IAAM,qBAAqB,MAAM;AAC7B,QAAM,YAAY,OAAO,aAAc;AACvC,WAAS,IAAI,GAAG,IAAI,UAAU,YAAY,KAAK;AAC3C,cAAU,YAAY,UAAU,WAAW,CAAC,CAAC;EACrD;AACA;AACA,IAAM,aAAa,CAAC,MAAM,UAAU;AAChC,QAAM,WAAW,SAAU,OAAO;AAC9B,QAAI,UAAU,QAAQ,MAAM,WAAW;AACnC;AACJ,QAAI,MAAM,WAAW,MAAM;AACvB,YAAM,YAAY,KAAK;AACvB,YAAM,aAAa,KAAK;IACpC,OACa;AACD,WAAK,YAAY,MAAM;AACvB,WAAK,aAAa,MAAM;IACpC;EACK;AACD,MAAI,CAAC,KAAK,UAAU;AAChB,SAAK,WAAW;EACxB;AACI,MAAI,CAAC,MAAM,UAAU;AACjB,UAAM,WAAW;EACzB;AACI,SAAO,MAAM;AACT,SAAK,WAAW;AAChB,UAAM,WAAW;EACpB;AACL;AAEA,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAe3B,IAAI;CACH,SAAUC,gBAAe;AACtBA,iBAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3CA,iBAAcA,eAAc,IAAI,IAAI,CAAC,IAAI;AACzCA,iBAAcA,eAAc,IAAI,IAAI,CAAC,IAAI;AACzCA,iBAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9CA,iBAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7CA,iBAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC/C,GAAG,kBAAkB,gBAAgB,CAAA,EAAG;AACxC,IAAM,YAAY,CAAC,WAAW;AAC1B,UAAQ,QAAM;IACV,KAAK,cAAc;AACf,aAAO;IACX,KAAK,cAAc;AACf,aAAO;IACX,KAAK,cAAc;AACf,aAAO;IACX;AACI,aAAO;EACnB;AACA;AACG,IAAC;CACH,SAAUC,eAAc;AAErBA,gBAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AAEhDA,gBAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AAChDA,gBAAaA,cAAa,OAAO,IAAI,CAAC,IAAI;AAC1CA,gBAAaA,cAAa,SAAS,IAAI,CAAC,IAAI;AAChD,GAAG,iBAAiB,eAAe,CAAA,EAAG;AC9L/B,IAAM,WAAsC,OAAO;AAEnD,IAAM,gBAA4C,OAAO;AAEzD,IAAM,aAA8C,OAAO;AAE3D,IAAM,iBAA4C,OAAO;AAEzD,IAAM,mBAA+C,OAAO;AAE5D,IAAM,wBAAoD,OAAO;AAEjE,IAAM,wBAAoD,OAAO;AAEjE,IAAM,cAGR,OAAO;AAEL,IAAM,mBAET,OAAO;AAEJ,IAAM,yBAET,OAAO;AAEJ,IAAM,oBAAkF,OAAO;AAE/F,IAAM,uBAAiG,OAAO;AChBxG,IAAA,QAAQ,MAAM,OAAO,QAAQ;AAE7B,IAAA,UAAU,MAAM,OAAO,UAAU;AAEjC,IAAAC,iBAAe,MAAM,OAAO,aAAa;AAEzC,IAAA,cAAc,MAAM,OAAO,cAAc;AAEzC,IAAA,gBAAgB,MAAM,OAAO,gBAAgB;AAE7C,IAAA,qBAAqB,MAAM,OAAO,qBAAqB;AAEvD,IAAA,qBAAqB,MAAM,OAAO,qBAAqB;AAEvD,IAAA,gBAAgB,MAAM,OAAO,gBAAgB;AAE7C,IAAA,sBAAsB,MAAM,OAAO,sBAAsB;AAEzD,IAAA,WAAW,MAAM,OAAO,WAAW;AAEnC,IAAA,YAAY,MAAM,OAAO,iBAAiB;AAE1C,IAAA,eAAe,MAAM,OAAO,oBAAoB;ACrCtD,IAAM,eAAe,MAAM;AAC1B,QAAA,UAAU,IAAI,KAAK;AAEzB,YAAU,MAAM;AACd,YAAQ,QAAQ;EAAA,CACjB;AAEM,SAAA;AACT;ACNO,IAAM,aAAa,CACxB,OACA,KACA,WACA,WACG;AACH,QAAM,QAAQ,KAAI,SAAA,OAAA,SAAA,MAAQ,GAAA,OAAQ,UAAA,OAAA,SAAA,OAAQ,aAAY;AAEtD;IACE,MAAM,SAAA,OAAA,SAAA,MAAQ,GAAA;IACd,MAAM;AACE,YAAA,QAAQ,MAAM,GAAG;IACzB;IACA,EAAE,MAAM,UAAA,OAAA,SAAA,OAAQ,UAAU;EAC5B;AAEA,UAAQ,WAAW,KAAK;AAC1B;ACjBa,IAAA,uBAAuB,CAAC,OAA+B,aAA2C;AACvG,QAAA,gBAAgB,CAAC,YAAsC;AAC3D,UAAM,WAAW,MAAM;AAEvB,aAAS,QAAQ;AAEjB,UAAM,QAAQ,SAAS,UAAU,MAAM,SAAS,QAAQ,CAAC;AAEzD,YAAQ,KAAK;EACf;AAEA,cAAY,aAAa;AAC3B;ACTO,IAAM,eAAe,CAAC;EAC3B;EACA;AACF,MAGM;AACJ,QAAM,YAAY,aAAa;AAE/B,QAAM,WAAW,SAAS,KAAK,MAAM,QAAQ;AAE7C,MAAI,WAAW;AAEf,cAAY,WAAW,MAAM,WAAW,MAAM,MAAM;AAEpD,QAAM,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAE9C,QAAM,cAAc,MAAM;AACpB,QAAA,CAAC,UAAU,MAAO;AAChB,UAAA,QAAQ,uBAAA,EAAyB,QAAQ,KAAK,SAAS,IAAI,KAAK,KAAK;EAC7E;AAEA,kBAAgB,WAAW;AAEpB,SAAA;AACT;AC1BO,IAAMC,qBAAqBA,CAAC;EACjCC;EACAC;EACAC;EACAC;EACAC;AASF,MAAM;AACJ,SAAAC,YAAA,OAAA;IAAA,SAGM,4GACCJ,YAAY,MAAMA,YAAY;IAAG,SAE7B;MACLK,OAAO,YAAYC,gBAAgB;MACnCC,QAAQ,YAAYD,gBAAgB;IACtC;EAAC,GAAA,CAAAF,YAAA,UAAA;IAAA,SAAA;IAAA,SAIQ;MACLI,OAAO,OAAOC,kBAAkB;MAChCC,iBAAiB,OAAOC,eAAe;IACxC;IAAA,WACQC,MAAM;AACbT,sBAAgBF,YAAYF,IAAI;AAChCG,uBAAAA,OAAAA,SAAAA,cAAgB,oBAAoBD,YAAYF,IAAAA;IAClD;EAAC,GAAA,CAAAc,gBAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAMT;AAEO,IAAMC,uBAAuBA,CAAC;EACnCb;EACAF;EACAG;EACAC;AAQF,MAAM;AACJ,SAAAC,YAAA,OAAA;IAAA,SAAA;IAAA,SAGW;MACLC,OAAO,YAAYC,gBAAgB;MACnCC,QAAQ,YAAYD,gBAAgB;IACtC;EAAC,GAAA,CAAAF,YAAA,UAAA;IAAA,SAAA;IAAA,SAIQ;MACLI,OAAO,OAAOC,kBAAkB;MAChCC,iBAAiB,OAAOC,eAAe;IACxC;IAAA,WACQC,MAAM;AACbT,sBAAgBF,YAAYF,IAAI;AAChCG,uBAAAA,OAAAA,SAAAA,cAAgB,oBAAoBD,YAAYF,IAAAA;IAClD;EAAC,GAAA,CAAAc,gBAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAMT;ACrFO,IAAME,gBAAgBA,MAAM;AACjC,SAAAX,YAAA,OAAA;IAAA,cAAA;IAAA,QAAA;IAAA,WAAA;IAAA,WAAA;IAAA,QAAA;EAAA,GAAA,CAAAA,YAAA,QAAA;IAAA,KAAA;EAAA,GAAA,IAAA,GAAAA,YAAA,QAAA;IAAA,KAAA;EAAA,GAAA,IAAA,CAAA,CAAA;AAMF;ACYA,IAAMY,aAAaA,CAAC;EAClBC;EACAC;EACAC;EACAC;EACAC;EACAC;AAQF,MAAM;AACJ,QAAMC,UAAUL,YAAAA,OAAAA,SAAAA,SAAUK;AAE1B,MAAIA,WAAAA,OAAAA,SAAAA,QAASC,eAAe;AAC1B,UAAMC,yBAAyBF,QAAQG;AAEvC,QAAIJ,kBAAkB,EAACJ,YAAAA,OAAAA,SAAAA,SAAUS,kBAAiB,OAAOC,yBAAyB,YAAY;AAC5FA,2BAAqB;QAAEV;QAAUD;QAASE;MAAS,CAAC;IACtD;AAEA,QAAIG,mBAAkBJ,YAAAA,OAAAA,SAAAA,SAAUS,gBAAe;AAC7C,aAAAvB,YAAA,QAAA;QAAA,SAAA;MAAA,GAAA,CAAAA,YAAA,QAAA;QAAA,iBAAA;QAAA,aAEmCc,SAASS;MAAa,GAAA,IAAA,GACpDF,2BAA2B9B,cAAckC,WAAOzB,YAAA,QAAA;QAAA,yCAAA;QAAA,SAGtCiB,aAAa,wBAAwB;QAAyC,SAC9E;UACLhB,OAAO,OAAOC,gBAAgB;UAC9BC,QAAQ,OAAOD,gBAAgB;QACjC;MAAA,GAACF,CAAAA,YAAAW,eAIJ,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IAGP,OAAO;AAEL,YAAMe,QAAQP,QAAQO;AACtB,YAAMC,OAAOd,QAAQe,MAAM,GAAGF,MAAMG,QAAQ;AAC5C,YAAMC,OAAOjB,QAAQe,MAAMF,MAAMG,UAAUH,MAAMG,WAAWH,MAAMK,MAAM;AACxE,YAAMC,OAAOnB,QAAQe,MAAMF,MAAMG,WAAWH,MAAMK,MAAM;AACxD,YAAME,SAASH,KAAKI,SAAS,IAAI;AACjC,YAAMC,QAAQF,SAASH,KAAKM,QAAQ,MAAM,EAAE,EAAEA,QAAQ,MAAM,EAAE,IAAIN;AAClE,aAAA9B,YAAA,QAAA;QAAA,SAAA;MAAA,GAAA,CAAAA,YAAA,QAAA;QAAA,oBAE4B0B,MAAMG;QAAQ,kBAAkBH,MAAMG,WAAWH,MAAMK;MAAAA,GAC5EJ,CAAAA,MAAI3B,YAAA,QAAA;QAAA,uBAAA;QAAA,SAAA;QAAA,SAII;UACLM,iBACES,aAAa,QAAQ,OAAOsB,yBAAyB,MAAM,OAAOC,yBAAyB;QAC/F;MAAA,GAECL,CAAAA,SAAMjC,YAAAuC,UAEFJ,MAAAA,CAAAA,OAAKnC,YAAA,QAAA;QAAA,uBAAA;MAAA,GAAA,CACqBwC,UAAUnB,sBAAsB,CAAC,CAAA,CAAA,CAAA,IAG9DS,IACD,CAAA,GAEFE,IAAI,CAAA,GAENX,2BAA2B9B,cAAckC,WAAOzB,YAAA,QAAA;QAAA,yCAAA;QAAA,SAGtCiB,aAAa,wBAAwB;QAAyC,SAC9E;UACLhB,OAAO,OAAOC,gBAAgB;UAC9BC,QAAQ,OAAOD,gBAAgB;QACjC;MAAA,GAACF,CAAAA,YAAAW,eAIJ,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IAGP;EACF;AAEA,MAAIO,kBAAkBF,aAAa,EAACA,aAAAA,OAAAA,SAAAA,UAAWyB,WAAU;AACvDzB,cAAUyB,WAAWC,qBAAqB1B,UAAU2B,KAAK;EAC3D;AAEA,MAAIzB,mBAAkBF,aAAAA,OAAAA,SAAAA,UAAWyB,WAAU;AACzC,WAAAzC,YAAA,QAAA;MAAA,SAAA;IAAA,GAAA,CAAAA,YAAA,QAAA;MAAA,iBAAA;MAAA,aAEmCgB,UAAUyB;IAAQ,GAAA,IAAA,CAAA,CAAA;EAGvD;AAEA,SAAAzC,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,CAA4Ca,OAAO,CAAA;AACrD;AAEA,IAAM+B,aAAaA,CAAC;EAClB/B;EACAC;EACAC;EACA8B;EACA5B;EACAC;AAQF,MAAM;;AACJ,MAAI,CAAC2B,YAAY;AACf,WAAA7C,YAAAY,YAAA;MAAA,WAEaC;MAAO,YACNC;MAAQ,YACRC;MAAQ,cACNE;MAAU,kBACNC;IAAc,GAAA,IAAA;EAGpC;AAEA,QAAMC,UAAUL,YAAAA,OAAAA,SAAAA,SAAUK;AAE1B,MAAIA,WAAAA,OAAAA,SAAAA,QAASC,eAAe;AAC1B,UAAMC,yBAAyBF,QAAQG;AAEvC,QAAIJ,kBAAkB,EAACJ,YAAAA,OAAAA,SAAAA,SAAUgC,mBAAkB,OAAOC,0BAA0B,YAAY;AAC9FA,4BAAsB;QAAEjC;QAAU+B;QAAY9B;MAAS,CAAC;IAC1D;AAEA,QAAIG,mBAAkBJ,YAAAA,OAAAA,SAAAA,SAAUgC,iBAAgB;AAC9C,aAAA9C,YAAA,QAAA;QAAA,SAAA;MAAA,GAAA,CAAAA,YAAA,QAAA;QAAA,iBAAA;QAAA,aAEmCc,SAASgC;MAAc,GAAA,IAAA,GACrDzB,2BAA2B9B,cAAckC,WAAOzB,YAAA,QAAA;QAAA,yCAAA;QAAA,SAGtCiB,aAAa,wBAAwB;QAAyC,SAC9E;UACLhB,OAAO,OAAOC,gBAAgB;UAC9BC,QAAQ,OAAOD,gBAAgB;QACjC;MAAA,GAACF,CAAAA,YAAAW,eAIJ,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IAGP,OAAO;AAEL,YAAMe,QAAQP,QAAQO;AAEtB,aAAA1B,YAAA,QAAA;QAAA,SAAA;MAAA,GAAA,CAAAA,YAAA,QAAA;QAAA,oBAE4B0B,MAAMG;QAAQ,kBAAkBH,MAAMG,WAAWH,MAAMK;MAAM,GAAA,EAClFc,KAAAA,WAAWG,aAAXH,OAAAA,SAAAA,GAAqBI,IAAI,CAAC;QAAEC;QAAMC;MAAS,GAAEC,UAAU;;AACtD,YAAIF,KAAKG,WAAW3B,MAAMG,YAAYH,MAAMG,WAAWH,MAAMK,SAASmB,KAAKI,YAAY;AACrF,iBAAAtD,YAAA,QAAA;YAAA,OAESoD;YAAK,cACEF,KAAKI;YAAU,YACjBJ,KAAKG;YAAQ,UAChBF,OAAAA,MAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,IAAqBvD,cAArBuD,OAAAA,SAAAA,IAAgCK,KAAK,GAAA;YAAI,UACzCL,KAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,GAAqBM;UAAAA,GAE3BP,CAAAA,KAAKP,KAAK,CAAA;QAGjB,OAAO;AACL,gBAAMe,SAAShC,MAAMG,WAAWqB,KAAKI;AACrC,gBAAMK,SAASD,SAAS,IAAI,IAAIA;AAChC,gBAAM/B,OAAOuB,KAAKP,MAAMf,MAAM,GAAG+B,MAAM;AACvC,gBAAM7B,OAAOoB,KAAKP,MAAMf,MAAM+B,QAAQD,SAAShC,MAAMK,MAAM;AAC3D,gBAAMC,OAAOkB,KAAKP,MAAMf,MAAM8B,SAAShC,MAAMK,MAAM;AACnD,gBAAM6B,UAAUjC,KAAKI,UAAUL,MAAMG,aAAaqB,KAAKI;AACvD,gBAAMO,QAAQ7B,KAAKD,UAAUmB,KAAKG,aAAa3B,MAAMG,WAAWH,MAAMK,SAAS;AAC/E,gBAAME,SAASH,KAAKI,SAAS,IAAI;AACjC,gBAAMC,QAAQF,SAASH,KAAKM,QAAQ,MAAM,EAAE,EAAEA,QAAQ,MAAM,EAAE,IAAIN;AAClE,iBAAA9B,YAAA,QAAA;YAAA,OAESoD;YAAK,cACEF,KAAKI;YAAU,YACjBJ,KAAKG;YAAQ,UAChBF,MAAAA,KAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,GAAqBvD,cAArBuD,OAAAA,SAAAA,GAAgCK,KAAK,GAAA;YAAI,UACzCL,KAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,GAAqBM;UAAAA,GAE3B9B,CAAAA,MAAI3B,YAAA,QAAA;YAAA,uBAAA;YAAA,SAGI;cACLM,iBACES,aAAa,QACT,OAAOsB,yBAAyB,MAChC,OAAOC,yBAAyB;cACtCwB,qBAAqBF,UAAU,UAAUG;cACzCC,wBAAwBJ,UAAU,UAAUG;cAC5CE,sBAAsBJ,SAAS5B,SAAS,UAAU8B;cAClDG,yBAAyBL,SAAS5B,SAAS,UAAU8B;YACvD;UAAA,GAEC9B,CAAAA,SAAMjC,YAAAuC,UAEFJ,MAAAA,CAAAA,OAAKnC,YAAA,QAAA;YAAA,uBAAA;UAAA,GAAA,CACqBwC,UAAUnB,sBAAsB,CAAC,CAG9DS,CAAAA,CAAAA,IAAAA,IACD,CAAA,GAEFE,IAAI,CAAA;QAGX;MAAA,CAAA,CACA,CAAA,GAEHX,2BAA2B9B,cAAckC,WAAOzB,YAAA,QAAA;QAAA,yCAAA;QAAA,SAGtCiB,aAAa,wBAAwB;QAAyC,SAC9E;UACLhB,OAAO,OAAOC,gBAAgB;UAC9BC,QAAQ,OAAOD,gBAAgB;QACjC;MAAA,GAACF,CAAAA,YAAAW,eAIJ,MAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IAGP;EACF;AAEA,MAAIO,kBAAkB,CAAC2B,WAAWJ,UAAU;AAC1CI,eAAWJ,WAAW0B,sBAAsBtB,UAAU;EACxD;AAEA,MAAI3B,kBAAkB2B,WAAWJ,UAAU;AACzC,WAAAzC,YAAA,QAAA;MAAA,SAAA;IAAA,GAAA,CAAAA,YAAA,QAAA;MAAA,iBAAA;MAAA,aAEmC6C,WAAWJ;IAAQ,GAAA,IAAA,CAAA,CAAA;EAGxD;AAEA,SAAAzC,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,EAEK6C,KAAAA,cAAAA,OAAAA,SAAAA,WAAYG,aAAZH,OAAAA,SAAAA,GAAsBI,IAAI,CAAC;IAAEC;IAAMC;EAAAA,GAAWC,UAAK;;AAAApD,WAAAA,YAAA,QAAA;MAAA,OAE3CoD;MAAK,cACEF,KAAKI;MAAU,YACjBJ,KAAKG;MAAQ,UAChBF,OAAAA,MAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,IAAqBvD,cAArBuD,OAAAA,SAAAA,IAAgCK,KAAK,GAAA;MAAI,UACzCL,KAAAA,WAAAA,OAAAA,SAAAA,QAASI,eAATJ,OAAAA,SAAAA,GAAqBM;IAAK,GAAA,CAEhCP,KAAKP,KAAK,CAAA;EAAA,CAAA,CAEb,CAAA;AAGR;AAEO,IAAMyB,cAAcA,CAAC;EAC1BtD;EACAD;EACAwD;EACArD;EACA6B;EACA5B;EACAqD;AASF,MAAM;;AACJ,QAAMC,WAAUzD,YAAAA,OAAAA,SAAAA,SAAU0D,UAASC,aAAaC;AAEhD,QAAMC,YAAW7D,YAAAA,OAAAA,SAAAA,SAAU0D,UAASC,aAAaG;AAEjD,QAAMC,kCAAgChC,KAAAA,cAAAA,OAAAA,SAAAA,WAAYG,aAAZH,OAAAA,SAAAA,GAAsBd,UAAS;AAErE,QAAM+C,qBAAmBT,KAAAA,YAAAA,OAAAA,SAAAA,SAAUU,wBAAVV,OAAAA,SAAAA,GAAAA,KAAAA,QAAAA,MAAqC;AAE9D,SAAArE,YAAA,OAAA;IAAA,SAAA;IAAA,SAIW;MACLgF,YAAY/D,aAAa,aAAa;MACtCgE,WAAWhE,aAAa,cAAc;IACxC;EAAC,GAAA,CAAAjB,YAAA,QAAA;IAAA,iBAGgBuE,UAAU,MAAMI,WAAW,MAAMZ;IAAS,SAAA;EAAA,GAAA,CAGxDQ,UAAU,MAAMI,WAAW,MAAM,GAAG,CAAA,GAEtCL,mBAAmBzB,cAAc,CAACgC,gCAA6B7E,YAAA4C,YAAA;IAAA,YAElD2B,UAAU,QAAQI,WAAW,QAAQZ;IAAS,WAC/ClD;IAAO,YACNC;IAAQ,cACN+B;IAAU,cACV5B;IAAU,kBACN6D;EAAAA,GAAgB9E,IAAAA,IAAAA,YAAAY,YAAA;IAAA,YAItB2D,UAAU,QAAQI,WAAW,QAAQZ;IAAS,WAC/ClD;IAAO,YACNC;IAAQ,aACPE;IAAS,cACRC;IAAU,kBACN6D;EAAAA,GAEnB,IAAA,CAAA,CAAA;AAGP;AC9UO,IAAMI,yBAAsC,gBAChDC,CAAAA,UAAsF;;AACrF,QAAMC,YAAYC,aAAc;AAEhC,QAAMC,kBAAkBC,mBAAoB;AAE5C,QAAMjB,kBAAkBkB,mBAAoB;AAE5C,QAAMC,mBAAmBC,oBAAqB;AAE9C,QAAMC,cAAcC,SAAS,MAC3BT,MAAMxF,SAASkG,UAAUC,MACrBX,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,IAC3C+B,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAClD;AAEA,QAAM6C,qBAAqBL,SAAS,MAAA;;AAAM,WAAA,CAAC,GAACD,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmBO;EAAAA,CAAI;AAEnE,QAAMC,uBAAuBP,SAAS,MAAMQ;;AAAAA,WAAAA,4BAA2BT,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmBO,IAAI;EAAA,CAAC;AAE/F,QAAMG,uBAAuBT,SAAS,MAAMD;;AAAAA,YAAAA,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmBW;EAAAA,CAAQ;AAEvE,QAAMC,wBAAwBX,SAAS,MAAMD;;AAAAA,YAAAA,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmB9F;EAAAA,CAAU;AAE1E,QAAM2G,oBAAoBC,IACxBtB,MAAMxF,SAASkG,UAAUC,MACrBX,MAAMd,SAASqC,kBAAiBf,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmB9F,UAAU,IAC7DsF,MAAMd,SAASsC,kBAAiBhB,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmB9F,UAAU,CACnE;AAEA,QAAM+G,mBAAmBH,IACvBtB,MAAMxF,SAASkG,UAAUC,MACrBX,MAAMd,SAASwC,iBAAgBlB,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmB9F,UAAU,IAC5DsF,MAAMd,SAASyC,iBAAgBnB,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmB9F,UAAU,CAClE;AAEAkH,uBAAqB5B,OAAQd,CAAAA,aAAa;;AACxCmC,sBAAkB7D,QAChBwC,MAAMxF,SAASkG,UAAUC,MACrBzB,SAASqC,kBAAiBf,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmB9F,UAAU,IACvDwE,SAASsC,kBAAiBhB,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmB9F,UAAU;AAE7D+G,qBAAiBjE,QACfwC,MAAMxF,SAASkG,UAAUC,MACrBzB,SAASwC,iBAAgBlB,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmB9F,UAAU,IACtDwE,SAASyC,iBAAgBnB,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmB9F,UAAU;EAC9D,CAAC;AAED,QAAME,kBAAkBA,CAACF,YAAoBF,SAAoByF,UAAU;IAAEzF;IAAYE;EAAuB,CAAC;AAEjH,SAAO,MAAM;;AACX,QAAIwG,qBAAqB1D,MAAO,QAAO;AAEvC,UAAM4B,YAAUoB,OAAAA,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmBO,SAAnBP,OAAAA,SAAAA,IAAyBnB,UAASC,aAAaC;AAE/D,UAAMC,aAAWgB,OAAAA,MAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,IAAmBO,SAAnBP,OAAAA,SAAAA,IAAyBnB,UAASC,aAAaG;AAEhE,UAAMoC,YAAYC,aAAa1C,SAASI,UAAUsB,mBAAmBtD,KAAK;AAE1E,UAAMuE,eAAeC,gBAAgB5C,SAASI,UAAUsB,mBAAmBtD,KAAK;AAEhF,WAAA3C,YAAA,MAAA;MAAA,aAEemF,MAAMtF;MAAU,cACfoG,mBAAmBtD,SAAS,CAAC4D,sBAAsB5D,QAAQ,SAAS;MAAO,aAC5EkD,UAAUV,MAAMxF,IAAI;MAAC,SACzB,eAAe4G,sBAAsB5D,QAAQ,WAAW;IAAA,GAE9D4D,CAAAA,sBAAsB5D,QAAK3C,YAAAuC,UAAAA,MAAAA,CAAAvC,YAAA,MAAA;MAAA,SAGf,aAAa6F,UAAUV,MAAMxF,IAAI,CAAC;MAAmG,SACrI;QACLW,iBAAiB4G;QACjB9G,OAAO,OAAO6F,mBAAmBtD,QAAQyE,2BAA2BC,yBAAyB;QAC7FpH,OAAO,OAAOqH,kBAAkB;QAChCC,UAAU,OAAOD,kBAAkB;QACnCE,UAAU,OAAOF,kBAAkB;MACrC;IAAA,GAECrB,CAAAA,mBAAmBtD,SAAS2C,gBAAgB3C,SAAK3C,YAAAN,oBAAA;MAAA,SAEvCyF,MAAM/B;MAAK,cACNuC,YAAYhD,MAAM9C;MAAU,QAClCsF,MAAMxF;MAAI,YACNwF,MAAMd;MAAQ,iBACToB;MAAgB,aAAA;MAAA,mBAEd1F;IAAe,GAAA,IAAA,GAEnCC,YAAA,QAAA;MAAA,iBAEgB2F,YAAYhD,MAAM9C;MAAU,SACpC;QAAE4H,SAAStB,qBAAqBxD,QAAQoB,SAAY;MAAI;IAAC,GAAA,CAE/D4B,YAAYhD,MAAM9C,UAAU,CAAA,CAAA,CAAA,GAAAG,YAAA,MAAA;MAAA,SAIxB,aAAa6F,UAAUV,MAAMxF,IAAI,CAAC;MAA8B,SAChE;QAAEW,iBAAiB0G;MAAU;IAAA,GAAChH,CAAAA,YAAAoE,aAAA;MAAA,cAGvB;MAAK,YACPe,MAAMd;MAAQ,aACfsB,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmBhD,UAAS;MAAE,aAC7BgD,KAAAA,YAAYhD,UAAZgD,OAAAA,SAAAA,GAAmBO;MAAI,aACtBU,iBAAiBjE;MAAK,cACrB6D,kBAAkB7D;MAAK,mBAClB2B,gBAAgB3B;IAAK,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAAA3C,YAAA,MAAA;MAAA,SAMnC,aAAa6F,UAAUV,MAAMxF,IAAI,CAAC;MAA0B,SAC5D;QAAEW,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAAA,GAAC1H,CAAAA,YAAAS,QAAAA,MAAAA,CAAAA,gBAIb,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAEkH,MAAM;EAAwBxC,OAAO,CAAC,YAAY,SAAS,cAAc,MAAM;AAAE,CACrF;ACtIO,IAAM,cAAc,CAAC,EAAE,UAAU,OAAA,MAA8D;AACpG,QAAM,KAAK,MAAM;AAEjB,QAAM,UAAU1F,eAAa;AAEvB,QAAA,QAAQ,IAAI,CAAC;AAEb,QAAA,eAAe,CAAC,aAAuC;AACvD,QAAA,CAAC,QAAQ,MAAO;AAEpB,QAAI,OAAO,OAAO;AAChB,YAAM,YAAY,SAAS,cAAc,aAAa,GAAG,KAAK,EAAE;AAEhE,YAAM,UAAU,aAAA,OAAA,SAAA,UAAW,cAAc,SAAS,KAAA;AAElD,UAAI,CAAC,QAAS;AAEd,YAAM,eAAe;AAErB,YAAM,KAAK,MAAM;AACT,cAAA,OAAO,WAAA,OAAA,SAAA,QAAS,sBAAA;AAChB,cAAA,SAAQ,QAAA,OAAA,SAAA,KAAM,UAAS;MAC/B;AAEG,SAAA;AAEH,YAAM,UAAU,MAAM;;AACP,qBAAA,kBAAkB,OAAO,EAAE;AACpC,YAAA,aAAa,kBAAkB,SAAS,GAAG;AAC7C,WAAA,KAAA,aAAa,sBAAb,OAAA,SAAA,GAAgC,WAAA;AAChC,uBAAa,gBAAgB,cAAc;AAC3C,iBAAO,aAAa;AACpB,iBAAO,aAAa;QAAA;MAExB;AAEA,UAAI,aAAa,mBAAmB;AACrB,qBAAA,kBAAkB,IAAI,EAAE;AAE5B,iBAAA,MAAM,QAAA,CAAS;AAExB;MAAA;AAGW,mBAAA,oBAAA,oBAAwB,IAAI;AAE5B,mBAAA,kBAAkB,IAAI,EAAE;AAE/B,YAAA,WAAW,IAAI,eAAe,MAAM,aAAa,kBAAkB,QAAQ,CAACmI,QAAOA,IAAG,CAAC,CAAC;AAE9F,mBAAa,oBAAoB;AAEjC,eAAS,QAAQ,YAAY;AAEhB,mBAAA,aAAa,gBAAgB,QAAQ;AAEzC,eAAA,MAAM,QAAA,CAAS;IAAA;EAE5B;AAEA,kBAAgB,CAAC,aAAa,aAAa,QAAQ,CAAC;AAE7C,SAAA;AACT;ACnEO,IAAM,gBAAgB,CAAC;EAC5B;EACA;EACA;EACA;AACF,MAKM;AACJ,QAAM,KAAK,MAAM;AAEjB,QAAM,YAAYnI,eAAa;AAEzB,QAAA,gBAAgB,CAAC,aAAuC;AACxD,QAAA,CAAC,UAAU,MAAO;AAEtB,QAAI,OAAO,OAAO;AAChB,UAAI,QAAQ,MAAM;MAAC;AAEnB,YAAM,YAAY,SAAS,cAAc,aAAa,GAAG,KAAK,EAAE;AAE1D,YAAA,WAAW,MAAM,MAAK,aAAA,OAAA,SAAA,UAAW,iBAAiB,SAAS,KAAA,MAAU,CAAA,CAAE;AAE7E,YAAM,WAAW,QAAQ,QAAQ,MAAM,MAAK,aAAA,OAAA,SAAA,UAAW,iBAAiB,WAAA,OAAA,SAAA,QAAS,KAAA,MAAU,CAAA,CAAE,IAAI;AAEjG,UAAI,SAAS,WAAW,KAAK,SAAS,WAAW,GAAG;AAC5C,cAAA,OAAO,SAAS,CAAC;AACjB,cAAA,OAAO,SAAS,CAAC;AAEjB,cAAA,WAAW,SAAS,CAAC;AACrB,cAAA,WAAW,SAAS,CAAC;AAE3B,cAAM,SAAS,KAAK,aAAa,WAAW,MAAM,KAAK,QAAQ,OAAO;AAEtE,cAAM,cAAc;AAEpB,cAAM,KAAK,MAAM;AACf,eAAK,MAAM,SAAS;AACpB,eAAK,MAAM,SAAS;AACd,gBAAA,QAAQ,KAAK,sBAAsB;AACnC,gBAAA,QAAQ,KAAK,sBAAsB;AACzC,gBAAM,YAAY,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAC5C,mBAAA,MAAM,SAAS,YAAY;AAC3B,mBAAA,MAAM,SAAS,YAAY;AACpC,mBAAS,aAAa,oBAAoB,OAAO,SAAS,CAAC;AAC3D,mBAAS,aAAa,oBAAoB,OAAO,SAAS,CAAC;QAC7D;AAEG,WAAA;AAEH,cAAM,UAAU,MAAM;;AACR,sBAAA,kBAAkB,OAAO,EAAE;AACnC,cAAA,YAAY,kBAAkB,SAAS,GAAG;AAC5C,aAAA,KAAA,YAAY,sBAAZ,OAAA,SAAA,GAA+B,WAAA;AAC/B,mBAAO,gBAAgB,cAAc;AACrC,mBAAO,YAAY;AACnB,mBAAO,YAAY;UAAA;QAEvB;AAEA,YAAI,YAAY,mBAAmB;AACrB,sBAAA,kBAAkB,IAAI,EAAE;AAE5B,kBAAA;AACR;QAAA;AAGU,oBAAA,oBAAA,oBAAwB,IAAI;AAE5B,oBAAA,kBAAkB,IAAI,EAAE;AAE9B,cAAA,WAAW,IAAI,eAAe,MAAM,YAAY,kBAAkB,QAAQ,CAACmI,QAAOA,IAAG,CAAC,CAAC;AAE7F,oBAAY,oBAAoB;AAEhC,iBAAS,QAAQ,MAAM;AAEhB,eAAA,aAAa,gBAAgB,QAAQ;AAEpC,gBAAA;MAAA;AAGD,eAAA,MAAM,MAAA,CAAO;IAAA;EAE1B;AAEA,kBAAgB,aAAa;AAC/B;ACrFO,IAAMC,wBAAqC,gBAC/C1C,CAAAA,UAAsF;AACrF,QAAM2C,aAAaC,cAAe;AAElC,QAAMC,QAAQC,SAAU;AAExB,QAAMC,eAAetC,SAAS,MAAM,kBAAkBT,MAAMtF,UAAU,mBAAmB;AAEzF,QAAMsI,sBAAsBvC,SAAS,MAAM,iBAAiBT,MAAMtF,UAAU,WAAW;AAEvF,QAAMuI,kBAAkBxC,SAAS,MAC/BT,MAAMxF,SAASkG,UAAUC,MAAM,4BAA4B,yBAC7D;AAEA,QAAMuC,UAAUzC,SAAS,MAAMT,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,CAAC;AAE3E,QAAMkF,UAAU1C,SAAS,MAAMT,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAAC;AAE5E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAMC,gBAAgB7C,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBY,YAAlBZ,OAAAA,SAAAA,IAA4BO,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAexI,UAAAA;EAAAA,CAAW;AAE3F,QAAM8I,gBAAgB/C,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBc,YAAlBd,OAAAA,SAAAA,GAA4BQ,QAAQ3F,MAAM9C,UAAAA;EAAAA,CAAW;AAE1F,QAAMgJ,cAAcjD,SAAS,MAAOT,MAAMxF,SAASkG,UAAUC,MAAMuC,QAAQ1F,QAAQ2F,QAAQ3F,KAAM;AAEjG,QAAMmG,kBAAkBlD,SAAS,MAAMiD,YAAYlG,MAAM2D,QAAQ;AAEjE,QAAMyC,gBAAgBnD,SAAS,MAAOT,MAAMxF,SAASkG,UAAUC,MAAM2C,cAAc9F,QAAQgG,cAAchG,KAAM;AAE/G,QAAMqG,oBAAoBpD,SAAS,MACjCT,MAAMxF,SAASkG,UAAUC,MAAMuC,QAAQ1F,MAAM9C,aAAayI,QAAQ3F,MAAM9C,UAC1E;AAEA,QAAMoJ,gBAAgBrD,SAAS,MAC7BsD,SACGT,cAAc9F,SAASgG,cAAchG,WAAW,CAACmG,gBAAgBnG,SAAS4F,aAAa5F,UAAUqF,MAAMmB,MAC1G,CACF;AAEA,QAAMC,gBAAgBxD,SACpB,OAAOT,MAAMxF,SAASkG,UAAUC,MAAM,CAAC,CAAC2C,cAAc9F,QAAQ,CAAC,CAACgG,cAAchG,UAAUsG,cAActG,KACxG;AAEA,QAAM0G,aAAazD,SACjB,MAAMC,UAAUkD,cAAcpG,QAAQwC,MAAMxF,OAAOwF,MAAMxF,SAASkG,UAAUyD,MAAMzD,UAAUC,MAAMD,UAAUyD,GAAG,CACjH;AAEAC,gBAAc;IACZC,UAAUtB;IACV/E,SAASgF;IACTxI,MAAM0J;IACNI,QAAQR;EACV,CAAC;AAED,QAAMhJ,QAAQyJ,YAAY;IACxBF,UAAUpB;IACVqB,QAAQL;EACV,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAACH,cAActG,MAAO,QAAO;AAEjC,WAAA3C,YAAA,MAAA;MAAA,aAEe,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,aAE5BgG,UAAUV,MAAMxF,IAAI;MAAC,SAAA;IAAA,GAAA,CAG/BoJ,cAAcpG,QAAK3C,YAAA,MAAA;MAAA,SACP,oBAAoB6F,UAAUV,MAAMxF,IAAI,CAAC;MAAc,WAAW;IAAC,GAAA,CAAAK,YAAA,OAAA;MAAA,aAE/D,GAAGmF,MAAMtF,UAAU;MAAiB,aACpCgG,UAAUV,MAAMxF,IAAI;MAAC,SAAA;MAAA,SAEzB;QAAEM,OAAOA,MAAM0C,QAAQ;MAAK;IAElC1C,GAAAA,CAAAA,MAAM0C,QAAQ,OACbqF,KAAAA,MAAMmB,WAANnB,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMwF,MAAMxF;MACZE,YAAYmJ,kBAAkBrG;MAC9BgH,MAAMZ,cAAcpG,MAAMgH;MAC1BC,UAAUzE,MAAMd,SAASwF;IAC1B,CAAA,EAAC,CAAA,CAAA,CAAA,IAAA7J,YAAA,MAAA;MAAA,SAKC,oBAAoB6F,UAAUV,MAAMxF,IAAI,CAAC;MAA8B,SACvE;QAAEW,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAAC,GAAA,CAAA1H,YAAA,OAAA;MAAA,aAEM,GAAGmF,MAAMtF,UAAU;MAAiB,aAAagG,UAAUV,MAAMxF,IAAI;IAAA,GAExF,IAAA,CAAA,CAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAEgI,MAAM;EAAuBxC,OAAO,CAAC,SAAS,YAAY,cAAc,MAAM;AAAE,CACpF;ACjHO,IAAM2E,aAAaA,CAAC;EAAElK;AAAiC,MAAM;AAClE,SAAAI,YAAA,OAAA;IAAA,eAAA;IAAA,UAAA;IAAA,WAAA;IAAA,WAAA;IAAA,SAAA;IAAA,SAC0FJ;EAAS,GAAA,CAAAI,YAAA,QAAA;IAAA,KAAA;EAAA,GAAA,IAAA,CAAA,CAAA;AAIrG;AAEO,IAAM+J,WAAWA,CAAC;EAAEnK;AAAkC,MAAM;AACjE,SAAAI,YAAA,OAAA;IAAA,eAAA;IAAA,UAAA;IAAA,WAAA;IAAA,WAAA;IAAA,SAAA;IAAA,SAC0FJ;EAAS,GAAA,CAAAI,YAAA,QAAA;IAAA,KAAA;EAAA,GAAA,IAAA,CAAA,CAAA;AAIrG;AAEO,IAAMgK,YAAYA,CAAC;EAAEpK;AAAkC,MAAM;AAClE,SAAAI,YAAA,OAAA;IAAA,eAAA;IAAA,UAAA;IAAA,WAAA;IAAA,WAAA;IAAA,SAAA;IAAA,SAC0FJ;EAAS,GAAA,CAAAI,YAAA,QAAA;IAAA,KAAA;EAAA,GAAA,IAAA,CAAA,CAAA;AAIrG;ACLA,IAAMiK,4BAAyC,gBAC5C9E,CAAAA,UAAsF;AACrF,QAAM+E,cAActE,SAAS,MAAMT,MAAMd,SAAS8F,iBAAiBhF,MAAM/B,KAAK,CAAC;AAE/E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAM4B,cAAcxE,SAAS,MAAM2C,aAAa5F,SAASuH,YAAYvH,SAASuH,YAAYvH,MAAM0H,SAAS;AAEzG,QAAMnC,eAAetC,SAAS,MAAM,iBAAiBT,MAAMtF,UAAU,SAAS;AAE9E,QAAMyK,oBAAoB1E,SAAS,MAAMT,MAAMxF,SAASkG,UAAUC,GAAG;AAErE,QAAMyE,wBAAwB3E,SAAS,MAAMC,UAAUA,UAAUC,GAAG,CAAC;AAErE,QAAM0E,qBAAqB5E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAM8H,OAAO;AAExF,QAAMC,oBAAoB9E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAMV,MAAM;AAEtF,QAAM0I,oBAAoB/E,SACxB,MAAMsE,YAAYvH,SAASwC,MAAMd,SAASuG,qBAAoB,KAAM,CAACV,YAAYvH,MAAM0H,SACzF;AAEA,QAAMQ,uBAAuBpE,IAC3ByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC,UAChG;AAEA,QAAM/B,gBAAgBxC,IACpByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS,cAC/E;AAEA/D,uBAAqB5B,OAAO,MAAM;AAChC0F,yBAAqBlI,QACnBuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC;AAE9F/B,kBAActG,QACZuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS;EAC/E,CAAC;AAED,QAAMG,0BAA0BrF,SAAS,MAAMT,MAAMxF,SAASkG,UAAUyD,OAAOL,cAActG,KAAK;AAElG4G,gBAAc;IACZC,UAAUtB;IACV/E,SAAS+E;IACTvI,MAAM4K;IACNd,QAAQwB;EACV,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAAChC,cAActG,SAAS,CAACgI,kBAAkBhI,MAAO,QAAO;AAE7D,WAAA3C,YAAA,MAAA;MAAA,aAEe,GAAGmF,MAAMtF,UAAU;MAAO,cAAA;MAAA,aAE1BgG,UAAUV,MAAMxF,IAAI;MAAC,SACzB;QAAEW,iBAAiB,OAAO4K,iBAAiB;MAAK;MAAA,SAAA;IAAA,GAGtDZ,CAAAA,kBAAkB3H,QAAK3C,YAAAuC,UAAAA,MAAAA,CAAAvC,YAAA,MAAA;MAAA,SAAA;MAAA,SAIX;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;QACtCnH,OAAO,OAAOqH,kBAAkB;QAChCC,UAAU,OAAOD,kBAAkB;QACnCE,UAAU,OAAOF,kBAAkB;MACrC;IAAA,GAEC8C,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIlEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEe,qBAAqBlI,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKjBQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,OAAOjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAUxDQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ3DQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKvE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAI9C;QACLI,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAM0H,cAAlBH,OAAAA,SAAAA,GAA6BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAvL,YAAA,MAAA;MAAA,SAAA;MAAA,WAO5D;MAAC,SACH;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,CAAAS,gBAI1D,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAEkH,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,cAAc,MAAM;AAAE,CAClF;AAEA,IAAMqG,4BAAyC,gBAC5CrG,CAAAA,UAAsF;AACrF,QAAM+E,cAActE,SAAS,MAAMT,MAAMd,SAAS8F,iBAAiBhF,MAAM/B,KAAK,CAAC;AAE/E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAMN,eAAetC,SAAS,MAAM,iBAAiBT,MAAMtF,UAAU,SAAS;AAE9E,QAAMuK,cAAcxE,SAAS,MAAM2C,aAAa5F,SAASuH,YAAYvH,SAASuH,YAAYvH,MAAM0H,SAAS;AAEzG,QAAMQ,uBAAuBpE,IAC3ByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC,UAChG;AAEA,QAAMR,qBAAqB5E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAM8H,OAAO;AAExF,QAAME,oBAAoB/E,SACxB,MAAMsE,YAAYvH,SAASwC,MAAMd,SAASuG,qBAAoB,KAAM,CAACV,YAAYvH,MAAM0H,SACzF;AAEA,QAAMK,oBAAoB9E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAMV,MAAM;AAEtF,QAAMgH,gBAAgBxC,IACpByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS,cAC/E;AAEA,QAAMP,wBAAwB3E,SAAS,MAAMC,UAAUV,MAAMxF,IAAI,CAAC;AAElE,QAAMsL,0BAA0BrF,SAAS,MAAMqD,cAActG,KAAK;AAElE4G,gBAAc;IACZC,UAAUtB;IACV/E,SAAS+E;IACTvI,MAAM4K;IACNd,QAAQwB;EACV,CAAC;AAEDlE,uBAAqB5B,OAAO,MAAM;AAChC0F,yBAAqBlI,QACnBuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC;AAE9F/B,kBAActG,QACZuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS;EAC/E,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAAC7B,cAActG,SAAS,CAACgI,kBAAkBhI,MAAO,QAAO;AAE7D,WAAA3C,YAAA,MAAA;MAAA,aAEe,GAAGmF,MAAMtF,UAAU;MAAO,cAAA;MAAA,aAE1BgG,UAAUV,MAAMxF,IAAI;MAAC,SACzB;QAAEW,iBAAiB,OAAO4K,iBAAiB;MAAK;MAAA,SAAA;IAAA,GAAA,CAAAlL,YAAA,MAAA;MAAA,SAAA;MAAA,SAK9C;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;QACtCnH,OAAO,OAAOqH,kBAAkB;QAChCC,UAAU,OAAOD,kBAAkB;QACnCE,UAAU,OAAOF,kBAAkB;MACrC;IAAA,GAEC8C,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIlEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEe,qBAAqBlI,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKjBQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,OAAOjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAUxDQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ3DQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKvE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAI9C;QACLI,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAM0H,cAAlBH,OAAAA,SAAAA,GAA6BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA;EAK1E;AACH,GACA;EAAE5D,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,cAAc,MAAM;AAAE,CAClF;AAEO,IAAMsG,sBAAmC,gBAC7CtG,CAAAA,UAAsF;AACrF,QAAMuG,eAAeC,QAAS;AAE9B,SAAO,MAAM;AACX,QAAID,aAAa/I,UAAUnD,aAAaoM,eAAeF,aAAa/I,UAAUnD,aAAaqM,OAAO;AAChG,aAAA7L,YAAAiK,2BAAA;QAAA,SAEW9E,MAAM/B;QAAK,QACZ+B,MAAMxF;QAAI,YACNwF,MAAMd;QAAQ,cACZc,MAAMtF;MAAU,GAAA,IAAA;IAGlC,OAAO;AACL,aAAAG,YAAAwL,2BAAA;QAAA,SAEWrG,MAAM/B;QAAK,QACZ+B,MAAMxF;QAAI,YACNwF,MAAMd;QAAQ,cACZc,MAAMtF;MAAU,GAAA,IAAA;IAGlC;EACD;AACH,GACA;EAAE8H,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,cAAc,MAAM;AAAE,CAClF;ACnVO,IAAM2G,wBAAqC,gBAC/C3G,CAAAA,UAAsF;AACrF,QAAM6C,QAAQC,SAAU;AAExB,QAAM8D,SAASC,UAAW;AAE1B,QAAM5G,YAAYC,aAAc;AAEhC,QAAMgD,UAAUzC,SAAS,MAAMT,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,CAAC;AAE3E,QAAMkF,UAAU1C,SAAS,MAAMT,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAAC;AAE5E,QAAM6I,gBAAgBrG,SACpB,MACEyC,QAAQ1F,MAAM9C,cACdkM,OAAOpJ,MAAMhD,SAASkG,UAAUC,OAChCiG,OAAOpJ,MAAM9C,eAAewI,QAAQ1F,MAAM9C,UAC9C;AAEA,QAAMqM,gBAAgBtG,SACpB,MACE0C,QAAQ3F,MAAM9C,cACdkM,OAAOpJ,MAAMhD,SAASkG,UAAUyD,OAChCyC,OAAOpJ,MAAM9C,eAAeyI,QAAQ3F,MAAM9C,UAC9C;AAEA,QAAM8F,cAAcC,SAAS,MAAOT,MAAMxF,SAASkG,UAAUC,MAAMuC,QAAQ1F,QAAQ2F,QAAQ3F,KAAM;AAEjG,QAAMmG,kBAAkBlD,SAAS,MAAMD,YAAYhD,MAAM2D,QAAQ;AAEjE,QAAM4B,eAAetC,SAAS,MAAM,kBAAkBT,MAAMtF,UAAU,mBAAmB;AAEzF,QAAMsI,sBAAsBvC,SAAS,MAAM,iBAAiBT,MAAMtF,UAAU,WAAW;AAEvF,QAAMuI,kBAAkBxC,SAAS,MAC/BT,MAAMxF,SAASkG,UAAUC,MAAM,4BAA4B,yBAC7D;AAEA,QAAMqG,gBAAgBvG,SAAS,MAAOT,MAAMxF,SAASkG,UAAUC,MAAMmG,cAActJ,QAAQuJ,cAAcvJ,KAAM;AAE/G,QAAMyJ,cAAcxG,SAClB,MAAMC,UAAUsG,gBAAgBhH,MAAMxF,OAAOwF,MAAMxF,SAASkG,UAAUC,MAAMD,UAAUyD,MAAMzD,UAAUC,GAAG,CAC3G;AAEA,QAAMmD,gBAAgBrD,SACpB,OAAO,CAAC,CAACqG,cAActJ,SAAS,CAAC,CAACuJ,cAAcvJ,UAAU,CAACmG,gBAAgBnG,SAAS,CAAC,CAACqF,MAAM+D,MAC9F;AAEA,QAAM3C,gBAAgBxD,SAAS,MAAMuG,cAAcxJ,SAAS,CAAC,CAACsG,cAActG,KAAK;AAEjF,QAAM0J,gBAAgBA,MAAMjH,UAAU,CAAA,CAAE;AAExCmE,gBAAc;IACZC,UAAUtB;IACV/E,SAASgF;IACTxI,MAAMyM;IACN3C,QAAQR;EACV,CAAC;AAED,QAAMhJ,QAAQyJ,YAAY;IACxBF,UAAUpB;IACVqB,QAAQL;EACV,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAACH,cAActG,MAAO,QAAO;AAEjC,WAAA3C,YAAA,MAAA;MAAA,aAEe,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,aAE5BgG,UAAUV,MAAMxF,IAAI;MAAC,SAAA;IAAA,GAAA,CAG/BwM,cAAcxJ,QAAK3C,YAAA,MAAA;MAAA,SACP,oBAAoB6F,UAAUV,MAAMxF,IAAI,CAAC;MAAc,WAAW;IAAC,GAAA,CAAAK,YAAA,OAAA;MAAA,aAE/D,GAAGmF,MAAMtF,UAAU;MAAiB,aACpCgG,UAAUV,MAAMxF,IAAI;MAAC,SAAA;MAAA,SAEzB;QAAEM,OAAOA,MAAM0C,QAAQ;MAAK;IAElC1C,GAAAA,CAAAA,MAAM0C,QAAQ,OACbqF,KAAAA,MAAM+D,WAAN/D,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMwF,MAAMxF;MACZE,YAAY8F,YAAYhD,MAAM9C;MAC9ByM,SAASD;IACV,CAAA,EAAC,CAAA,CAAA,CAAA,IAAArM,YAAA,MAAA;MAAA,SAKC,oBAAoB6F,UAAUV,MAAMxF,IAAI,CAAC;MAA8B,SACvE;QAAEW,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAAC,GAAA,CAAA1H,YAAA,OAAA;MAAA,aAEM,GAAGmF,MAAMtF,UAAU;MAAiB,aAAagG,UAAUV,MAAMxF,IAAI;IAAA,GAExF,IAAA,CAAA,CAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAEgI,MAAM;EAAuBxC,OAAO,CAAC,YAAY,SAAS,cAAc,MAAM;AAAE,CACpF;AC7FA,IAAMoH,qBAAoC,gBACvCpH,CAAAA,UAKK;AACJ,QAAMvF,YAAYgG,SAAS,MAAOT,MAAMxF,SAASkG,UAAUyD,MAAM,mBAAmB,gBAAiB;AAErG,QAAMkD,QAAQ/F,IAAIgG,qBAAqBtH,MAAMd,QAAQ,CAAC;AAEtD0C,uBAAqB5B,OAAQd,CAAAA,aAAa;AACxCmI,UAAM7J,QAAQ8J,qBAAqBpI,QAAQ;EAC7C,CAAC;AAED,QAAMqI,cAAcvH,MAAMuH;AAE1B,QAAMC,cAAeC,CAAAA,MAAkB;;AACrC,QAAIC,MAAMD,EAAEE;AAEZ,QAAID,QAAOA,OAAAA,OAAAA,SAAAA,IAAKE,cAAa,UAAU;AACrCC,yBAAoB;AACpB;IACF;AAEA,WAAOH,OAAOA,eAAeI,aAAa;AACxC,YAAMC,QAAQL,IAAIM,aAAa,YAAY;AAC3C,UAAID,OAAO;AACT,YAAIA,UAAU,YAAYA,UAAU,UAAUA,UAAU,UAAU;AAChE,cAAIR,YAAYU,YAAYrJ,QAAW;AACrC2I,wBAAYU,UAAUrJ;AACtBoB,aAAAA,KAAAA,MAAMkI,aAANlI,OAAAA,SAAAA,GAAAA,KAAAA,OAAiBpB,MAAAA;AACjBiJ,+BAAoB;UACtB;QACF,OAAO;AACL,cAAIN,YAAYU,YAAYjI,MAAMxF,MAAM;AACtC+M,wBAAYU,UAAUjI,MAAMxF;AAC5BwF,aAAAA,KAAAA,MAAMkI,aAANlI,OAAAA,SAAAA,GAAAA,KAAAA,OAAiBA,MAAMxF,IAAAA;AACvBqN,+BAAoB;UACtB;QACF;AACA;MACF;AACAH,YAAMA,IAAIS;IACZ;EACD;AAED,SAAO,MAAM;AACX,WAAAtN,YAAA,SAAA;MAAA,SACgBJ,UAAU+C,QAAQ;MAA0C,aAAakD,UAAUV,MAAMxF,IAAI;IAAA,GAACK,CAAAA,YAAAA,YAAAA,MAAAA,CAAAA,YAAA,OAAA;MAAA,SAE5F,cAAc6F,UAAUV,MAAMxF,IAAI,CAAC;IAAU,GAAA,IAAA,GAAAK,YAAA,OAAA;MAAA,SAC7C,cAAc6F,UAAUV,MAAMxF,IAAI,CAAC;IAAc,GAAA,IAAA,CAAA,CAAA,GAAAK,YAAA,SAAA;MAAA,SAAA;IAAA,GAAAA,CAAAA,YAAAA,MAAAA,MAAAA,CAAAA,YAAA,MAAA;MAAA,SAAA;IAAA,GAI1C6F,CAAAA,UAAUV,MAAMxF,IAAI,GAACc,gBAAA,cAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;IACrB6F,GAAAA,CAAAA,UAAUV,MAAMxF,IAAI,GAACc,gBAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,SAAA;MAAA,SAAA;MAAA,eAGgB2M;IAAAA,GACvDH,CAAAA,MAAM7J,MAAMM,IAAKsK,CAAAA,SAAIvN,YAAAuC,UAAA;MAAA,OACLgL,KAAKnK;IAAAA,GAAKpD,CAAAA,YAAAyL,qBAAA;MAAA,SAEd8B,KAAKnK;MAAK,QACX+B,MAAMxF;MAAI,cACJ4N,KAAK1N;MAAU,YACjBsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAAkF,wBAAA;MAAA,SAGjBqI,KAAKnK;MAAK,QACX+B,MAAMxF;MAAI,cACJ4N,KAAK1N;MAAU,YACjBsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAA8L,uBAAA;MAAA,SAGjByB,KAAKnK;MAAK,QACX+B,MAAMxF;MAAI,cACJ4N,KAAK1N;MAAU,YACjBsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAA6H,uBAAA;MAAA,SAGjB0F,KAAKnK;MAAK,QACX+B,MAAMxF;MAAI,cACJ4N,KAAK1N;MAAU,YACjBsF,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAG7B,GAACrE,YAAAyL,qBAAA;MAAA,QAEMtG,MAAMxF;MAAI,SACTwF,MAAMd,SAASmJ;MAAe,cACzBrI,MAAMd,SAASmJ;MAAe,YAChCrI,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA;EAKjC;AACH,GACA;EAAEsD,MAAM;EAAsBxC,OAAO,CAAC,YAAY,QAAQ,YAAY,aAAa;AAAE,CACvF;AAEO,IAAMsI,sBAAqC,gBAC/CtI,CAAAA,UAAkC;AACjC,QAAMuI,YAAYjO,aAAc;AAEhC,QAAMkO,OAAOlH,IAAqB;AAElC,QAAMmH,OAAOnH,IAAqB;AAElC,QAAMoH,WAAWpH,IAAuB;AAExC,QAAMqH,UAAUlI,SAAS,MAAMmI,KAAKC,IAAI7I,MAAMd,SAASmJ,iBAAiBrI,MAAMd,SAAS4J,cAAc,EAAEC,SAAQ,CAAE;AAEjH,QAAMC,iBAAkBC,CAAAA,YAAsC;AAC5D,QAAI,CAACV,UAAU/K,MAAO;AACtB,UAAM0L,OAAOV,KAAKhL;AAClB,UAAM2L,QAAQV,KAAKjL;AACnB,QAAI,CAAC0L,QAAQ,CAACC,MAAO;AACrB,UAAMC,QAAQC,WAAWH,MAAMC,KAAK;AACpCF,YAAQG,KAAK;EACd;AAEDE,kBAAgBN,cAAc;AAE9B,QAAMd,WAAY1N,CAAAA,SAAqB;AACrC,UAAMkN,MAAMgB,SAASlL;AAErB,QAAI,CAACkK,IAAK;AAEV,QAAI,CAAClN,MAAM;AACTkN,UAAI6B,cAAc;IACpB,OAAO;AACL,YAAMC,KAAK,YAAYxJ,MAAMd,SAASuK,MAAK,CAAE;AAC7C/B,UAAI6B,cAAc,IAAIC,EAAE;GAAiDA,EAAE;GAA+CA,EAAE;IAC9H;EACD;AAED,QAAME,WAAWC,YAAa;AAE9B,QAAMpC,cAAc;IAAEU,SAASrJ;EAAoC;AAEnE,QAAMgL,OAAOnJ,SAAS,OAAO;IAAEiJ,UAAUA,SAASlM,QAAQ;IAAMqM,YAAY;EAA6B,EAAE;AAE3G,QAAM/O,QAAQgP,aAAa;IAAE1D,MAAMuC;IAASiB;EAAK,CAAC;AAElD,QAAMG,gBAAgBtJ,SAAS,MAAMmI,KAAKC,IAAI,IAAI/N,MAAM0C,QAAQ,EAAE,CAAC;AAEnE,SAAO,MAAM;AACX,WAAA3C,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,CAAAA,YAAA,SAAA;MAAA,qBAAA;MAAA,OAEkC6N;IAAQ,GAAA,IAAA,GAAA7N,YAAA,OAAA;MAAA,SAAA;MAAA,OAG/B2N;MAAI,SACF;QACL,CAACrG,kBAAkB,GAAG,GAAGyG,KAAKoB,MAAMD,cAAcvM,KAAK,CAAC;QACxDyM,qBAAqB;QACrBJ,YAAY;QACZH,UAAU,OAAO3O,gBAAgB;MACnC;IAAA,GAACF,CAAAA,YAAAuM,oBAAA;MAAA,QAGO1G,UAAUC;MAAG,YACTX,MAAMd;MAAQ,YACdgJ;MAAQ,eACLX;IAAW,GAAA,IAAA,CAAA,CAAA,GAAA1M,YAAA,OAAA;MAAA,SAAA;MAAA,SAGkB;QAAEM,iBAAiB,OAAO+O,eAAe;MAAI;IAAC,GAAA,IAAA,GAAArP,YAAA,OAAA;MAAA,SAAA;MAAA,OAGrF4N;MAAI,SACF;QACL,CAACtG,kBAAkB,GAAG,GAAGyG,KAAKoB,MAAMD,cAAcvM,KAAK,CAAC;QACxDyM,qBAAqB;QACrBJ,YAAY;QACZH,UAAU,OAAO3O,gBAAgB;MACnC;IAAA,GAACF,CAAAA,YAAAuM,oBAAA;MAAA,QAGO1G,UAAUyD;MAAG,YACTnE,MAAMd;MAAQ,YACdgJ;MAAQ,eACLX;IAAW,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA;EAKjC;AACH,GACA;EAAE/E,MAAM;EAAuBxC,OAAO,CAAC,UAAU;AAAE,CACrD;ACtMO,IAAMD,uBAAsC,gBAChDC,CAAAA,UAAqE;;AACpE,QAAMC,YAAYC,aAAc;AAEhC,QAAMC,kBAAkBC,mBAAoB;AAE5C,QAAMjB,kBAAkBkB,mBAAoB;AAE5C,QAAMC,mBAAmBC,oBAAqB;AAE9C,QAAM2C,UAAUzC,SAAS,MAAMT,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,CAAC;AAE3E,QAAMkF,UAAU1C,SAAS,MAAMT,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAAC;AAE5E,QAAMkM,gBAAgB7I,IAAItB,MAAMd,SAASqC,kBAAiB2B,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAexI,UAAU,CAAC;AAEpF,QAAM0P,gBAAgB9I,IAAItB,MAAMd,SAASsC,kBAAiB2B,KAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,GAAezI,UAAU,CAAC;AAEpF,QAAM2P,eAAe/I,IAAItB,MAAMd,SAASwC,gBAAgBwB,QAAQ1F,MAAM9C,UAAU,CAAC;AAEjF,QAAM4P,eAAehJ,IAAItB,MAAMd,SAASyC,gBAAgBwB,QAAQ3F,MAAM9C,UAAU,CAAC;AAEjF,QAAM6P,UAAU9J,SAAS,MAAM;;AAAA,WAAA,CAAC,GAACyC,MAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,IAAenC,SAAQ,CAAC,GAACoC,MAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,IAAepC;EAAAA,CAAI;AAE7E,QAAMyJ,YAAY/J,SAChB,MAAMQ;;AAAAA,WAAAA,4BAA2BiC,MAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,IAAenC,IAAI,KAAKE,4BAA2BkC,MAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,IAAepC,IAAI;EAAA,CACzG;AAEA,QAAM0J,YAAYhK,SAAS,MAAA;;AAAMyC,aAAAA,MAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,IAAe/B,eAAYgC,MAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,IAAehC;EAAAA,CAAQ;AAEnFS,uBAAqB5B,OAAQd,CAAAA,aAAa;;AACxCiL,kBAAc3M,QAAQ0B,SAASqC,kBAAiB2B,MAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,IAAexI,UAAU;AAEzE0P,kBAAc5M,QAAQ0B,SAASsC,kBAAiB2B,MAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,IAAezI,UAAU;AAEzE2P,iBAAa7M,QAAQ0B,SAASwC,gBAAgBwB,QAAQ1F,MAAM9C,UAAU;AAEtE4P,iBAAa9M,QAAQ0B,SAASyC,gBAAgBwB,QAAQ3F,MAAM9C,UAAU;EACxE,CAAC;AAED,QAAME,kBAAkBA,CAACF,YAAoBF,SAAoByF,UAAU;IAAEzF;IAAYE;EAAuB,CAAC;AAEjH,SAAO,MAAM;;AACX,QAAI+P,UAAUjN,MAAO,QAAO;AAE5B,UAAMkN,aAAa,CAAC,GAACxH,MAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,IAAexI;AAEpC,UAAMiQ,aAAa,CAAC,GAACxH,MAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,IAAezI;AAEpC,UAAMkQ,oBAAkB1H,MAAAA,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAenC,SAAfmC,OAAAA,SAAAA,GAAqB7D,UAASC,aAAaG;AAEnE,UAAMoL,mBAAiB1H,MAAAA,KAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,GAAepC,SAAfoC,OAAAA,SAAAA,GAAqB9D,UAASC,aAAaC;AAElE,UAAMuL,mBAAmBhJ,aAAa,OAAO8I,iBAAiBL,QAAQ/M,KAAK;AAE3E,UAAMuN,kBAAkB/I,gBAAgB,OAAO4I,iBAAiBL,QAAQ/M,KAAK;AAE7E,UAAMwN,mBAAmBlJ,aAAa+I,gBAAgB,OAAON,QAAQ/M,KAAK;AAE1E,UAAMyN,kBAAkBjJ,gBAAgB6I,gBAAgB,OAAON,QAAQ/M,KAAK;AAE5E,WAAA3C,YAAA,MAAA;MAAA,aACiBmF,MAAMtF;MAAU,cAAc6P,QAAQ/M,QAAQ,SAAS;MAAO,SAAA;IAAA,GAAA,CAC1EkN,aAAU7P,YAAAuC,UAAAA,MAAAA,CAAAvC,YAAA,MAAA;MAAA,SAAA;MAAA,SAIE;QACLM,iBAAiB4P;QACjB9P,OAAO,OAAOsP,QAAQ/M,QAAQyE,2BAA2BC,yBAAyB;MACpF;IAAA,GAECqI,CAAAA,QAAQ/M,SAAS2C,gBAAgB3C,SAAK3C,YAAAN,oBAAA;MAAA,SAE5ByF,MAAM/B;MAAK,cACNiF,QAAQ1F,MAAM9C;MAAU,QAC9BgG,UAAUC;MAAG,YACTX,MAAMd;MAAQ,iBACToB;MAAgB,aAAA;MAAA,mBAEd1F;IAAe,GAAA,IAAA,GAEnCC,YAAA,QAAA;MAAA,iBACoBqI,QAAQ1F,MAAM9C;MAAU,SAAS;QAAE4H,SAASkI,UAAUhN,QAAQoB,SAAY;MAAI;IAAC,GAAA,CACjGsE,QAAQ1F,MAAM9C,UAAU,CAAA,CAAA,CAAA,GAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,SAKpB;QAAEM,iBAAiB2P;MAAkB;MAAA,aACjCpK,UAAUA,UAAUC,GAAG;IAAA,GAEjC4J,CAAAA,QAAQ/M,SAAS2C,gBAAgB3C,SAAK3C,YAAAN,oBAAA;MAAA,SAE5ByF,MAAM/B;MAAK,cACNiF,QAAQ1F,MAAM9C;MAAU,QAC9BgG,UAAUC;MAAG,YACTX,MAAMd;MAAQ,iBACToB;MAAgB,aAAA;MAAA,mBAEd1F;IAAAA,GAEpB,IAAA,GAAAC,YAAAoE,aAAA;MAAA,cAEa;MAAI,YACNe,MAAMd;MAAQ,YACfgE,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAe1F;MAAK,aACnB0F,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAenC;MAAI,aAClBsJ,aAAa7M;MAAK,cACjB2M,cAAc3M;MAAK,mBACd2B,gBAAgB3B;IAAK,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAAA3C,YAAA,MAAA;MAAA,SAAA;MAAA,SAOnC;QAAEM,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAAA,GAAC1H,CAAAA,YAAAS,QAAAA,MAAAA,CAAAA,gBAIb,GAAA,CAAA,CAAA,CAAA,CAAA,GACAqP,aAAU9P,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,MAAA;MAAA,SAAA;MAAA,SAIE;QACLM,iBAAiB8P;QACjBhQ,OAAO,OAAOsP,QAAQ/M,QAAQyE,2BAA2BC,yBAAyB;QAClFgJ,iBAAiB,OAAOhB,eAAe;QACvCiB,iBAAiB;MACnB;IAAA,GAECZ,CAAAA,QAAQ/M,SAAS2C,gBAAgB3C,SAAK3C,YAAAN,oBAAA;MAAA,SAE5ByF,MAAM/B;MAAK,cACNkF,QAAQ3F,MAAM9C;MAAU,QAC9BgG,UAAUyD;MAAG,YACTnE,MAAMd;MAAQ,iBACToB;MAAgB,aAAA;MAAA,mBAEd1F;IAAe,GAAA,IAAA,GAEnCC,YAAA,QAAA;MAAA,iBACoBsI,QAAQ3F,MAAM9C;MAAU,SAAS;QAAE4H,SAASkI,UAAUhN,QAAQoB,SAAY;MAAI;IAAC,GAAA,CACjGuE,QAAQ3F,MAAM9C,UAAU,CAAA,CAAA,CAAA,GAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,SAKpB;QAAEM,iBAAiB6P;MAAkB;MAAA,aACjCtK,UAAUA,UAAUyD,GAAG;IAAA,GAEjCoG,CAAAA,QAAQ/M,SAAS2C,gBAAgB3C,SAAK3C,YAAAN,oBAAA;MAAA,SAE5ByF,MAAM/B;MAAK,cACNkF,QAAQ3F,MAAM9C;MAAU,QAC9BgG,UAAUyD;MAAG,YACTnE,MAAMd;MAAQ,iBACToB;MAAgB,aAAA;MAAA,mBAEd1F;IAAAA,GAEpB,IAAA,GAAAC,YAAAoE,aAAA;MAAA,cAEa;MAAI,YACNe,MAAMd;MAAQ,aACfiE,KAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,GAAe3F,UAAS;MAAE,aACzB2F,KAAAA,QAAQ3F,UAAR2F,OAAAA,SAAAA,GAAepC;MAAI,aAClBuJ,aAAa9M;MAAK,cACjB4M,cAAc5M;MAAK,mBACd2B,gBAAgB3B;IAAK,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAAA3C,YAAA,MAAA;MAAA,SAAA;MAAA,SAOnC;QACLM,iBAAiB,OAAOoH,WAAW;QACnC2I,iBAAiB,OAAOhB,eAAe;QACvCiB,iBAAiB;MAClB;MAAA,WACQ;IAAA,GAACtQ,CAAAA,YAAAS,QAAAA,MAAAA,CAAAA,gBAIb,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAEkH,MAAM;EAAwBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC7E;AC3MO,IAAM0C,sBAAqC,gBAC/C1C,CAAAA,UAAqE;AACpE,QAAM2C,aAAaC,cAAe;AAElC,QAAMC,QAAQC,SAAU;AAExB,QAAMI,UAAUzC,SAAS,MAAMT,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,CAAC;AAE3E,QAAMkF,UAAU1C,SAAS,MAAMT,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAAC;AAE5E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAMC,gBAAgB7C,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBY,YAAlBZ,OAAAA,SAAAA,IAA4BO,KAAAA,QAAQ1F,UAAR0F,OAAAA,SAAAA,GAAexI,UAAAA;EAAAA,CAAW;AAE3F,QAAM8I,gBAAgB/C,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBc,YAAlBd,OAAAA,SAAAA,GAA4BQ,QAAQ3F,MAAM9C,UAAAA;EAAAA,CAAW;AAE1F,QAAM+P,YAAYhK,SAAS,MAAMyC,QAAQ1F,MAAM2D,YAAYgC,QAAQ3F,MAAM2D,QAAQ;AAEjF,QAAM2C,gBAAgBrD,SAAS,MAC7BsD,SAAST,cAAc9F,SAASgG,cAAchG,WAAW,CAACiN,UAAUjN,SAAS4F,aAAa5F,UAAUqF,MAAMmB,MAAM,CAClH;AAEA,SAAO,MAAM;;AACX,QAAI,CAACF,cAActG,MAAO,QAAO;AAEjC,UAAM4N,oBAAoB9H,cAAc9F,SACpCqF,KAAAA,MAAMmB,WAANnB,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUC;MAChBjG,YAAYwI,QAAQ1F,MAAM9C;MAC1B8J,MAAMlB,cAAc9F,MAAMgH;MAC1BC,UAAUzE,MAAMd,SAASwF;IAC1B,CAAA,IACD;AAEJ,UAAM2G,oBAAoB7H,cAAchG,SACpCqF,KAAAA,MAAMmB,WAANnB,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUyD;MAChBzJ,YAAYyI,QAAQ3F,MAAM9C;MAC1B8J,MAAMhB,cAAchG,MAAMgH;MAC1BC,UAAUzE,MAAMd,SAASwF;IAC1B,CAAA,IACD;AAEJ,WAAA7J,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,SAAA;IAAA,GACxC0Q,CAAAA,oBAAiBvQ,YAAA,MAAA;MAAA,SAAA;MAAA,WACsC;IAAC,GAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;IACduQ,GAAAA,CAAAA,iBAAiB,CAAA,CAAA,CAAA,IAAAvQ,YAAA,MAAA;MAAA,SAAA;MAAA,SAKjD;QAAEM,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAEZ,GAAA,IAAA,GACA8I,oBAAiBxQ,YAAA,MAAA;MAAA,SAAA;MAAA,WAGL;MAAC,SACH;QAAEqQ,iBAAiB,OAAOhB,eAAe;QAAKiB,iBAAiB;MAAQ;IAAC,GAAA,CAAAtQ,YAAA,OAAA;MAAA,SAAA;IAExCwQ,GAAAA,CAAAA,iBAAiB,CAAA,CAAA,CAAA,IAAAxQ,YAAA,MAAA;MAAA,SAAA;MAAA,SAKjD;QACLM,iBAAiB,OAAOoH,WAAW;QACnC2I,iBAAiB,OAAOhB,eAAe;QACvCiB,iBAAiB;MAClB;MAAA,WACQ;IAAA,GAEZ,IAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAE3I,MAAM;EAAuBxC,OAAO,CAAC,SAAS,YAAY,YAAY;AAAE,CAC5E;AC3EA,IAAM8E,0BAAyC,gBAC5C9E,CAAAA,UAAqE;AACpE,QAAM+E,cAActE,SAAS,MAAMT,MAAMd,SAAS8F,iBAAiBhF,MAAM/B,KAAK,CAAC;AAE/E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAM4B,cAAcxE,SAAS,MAAM2C,aAAa5F,SAASuH,YAAYvH,SAASuH,YAAYvH,MAAM0H,SAAS;AAEzG,QAAMQ,uBAAuBpE,IAC3ByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC,UAChG;AAEA,QAAM/B,gBAAgBxC,IACpByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS,cAC/E;AAEA,QAAMN,qBAAqB5E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAM8H,OAAO;AAExF,QAAMC,oBAAoB9E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAMV,MAAM;AAEtF,QAAM0I,oBAAoB/E,SACxB,MAAMsE,YAAYvH,SAASwC,MAAMd,SAASuG,qBAAoB,KAAM,CAACV,YAAYvH,MAAM0H,SACzF;AAEAtD,uBAAqB5B,OAAO,MAAM;AAChC0F,yBAAqBlI,QACnBuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC;AAE9F/B,kBAActG,QACZuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS;EAC/E,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAAC7B,cAActG,SAAS,CAACgI,kBAAkBhI,MAAO,QAAO;AAE7D,WAAA3C,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAO,cAAA;MAAA,SAAA;IAAA,GAAA,CAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,SAG9B;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;MACxC;IAAA,GAECgD,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIlEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEe,qBAAqBlI,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKjBQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,OAAOjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAUxDQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ3DQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKvE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAK;MAAA,WAC9C;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAID;QACLI,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAM0H,cAAlBH,OAAAA,SAAAA,GAA6BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA;EAK1E;AACH,GACA;EAAE5D,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC1E;AAEA,IAAMqG,0BAAyC,gBAC5CrG,CAAAA,UAAqE;AACpE,QAAM+E,cAActE,SAAS,MAAMT,MAAMd,SAAS8F,iBAAiBhF,MAAM/B,KAAK,CAAC;AAE/E,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAM4B,cAAcxE,SAAS,MAAM2C,aAAa5F,SAASuH,YAAYvH,SAASuH,YAAYvH,MAAM0H,SAAS;AAEzG,QAAMQ,uBAAuBpE,IAC3ByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC,UAChG;AAEA,QAAM/B,gBAAgBxC,IACpByD,YAAYvH,SACVuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS,cAC/E;AAEA,QAAMN,qBAAqB5E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAM8H,OAAO;AAExF,QAAME,oBAAoB/E,SACxB,MAAMsE,YAAYvH,SAASwC,MAAMd,SAASuG,qBAAoB,KAAM,CAACV,YAAYvH,MAAM0H,SACzF;AAEA,QAAMK,oBAAoB9E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAMV,MAAM;AAEtF8E,uBAAqB5B,OAAO,MAAM;AAChC0F,yBAAqBlI,QACnBuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUS,iBAAiBZ,YAAYvH,MAAM0H,UAAUU,mBAAmBC;AAE9F/B,kBAActG,QACZuH,YAAYvH,SACZuH,YAAYvH,MAAM0H,aAClBH,YAAYvH,MAAM0H,UAAUU,mBAAmBb,YAAYvH,MAAM0H,UAAUS;EAC/E,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAAC7B,cAActG,SAAS,CAACgI,kBAAkBhI,MAAO,QAAO;AAE7D,WAAA3C,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAO,cAAA;MAAA,SAAA;IAAA,GAAA,CAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,SAG9B;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;MACxC;IAAA,GAECgD,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIlEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEe,qBAAqBlI,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKjBQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,OAAOjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAUxDQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ3DQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKvE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAI9C;QACLI,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAM0H,cAAlBH,OAAAA,SAAAA,GAA6BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,GAAAvL,YAAA,MAAA;MAAA,SAAA;MAAA,SAK5D;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;QACtCiJ,iBAAiB,OAAOhB,eAAe;QACvCiB,iBAAiB;MACnB;IAAA,GAEClG,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIlEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEe,qBAAqBlI,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKjBQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,OAAOjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAUxDQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,QAAQjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ3DQ,MAAM2E,MAAMd,SAAS+G,kBAAkB,MAAMjG,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKvE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAI9C;QACLI,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAM0H,cAAlBH,OAAAA,SAAAA,GAA6BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA;EAK1E;AACH,GACA;EAAE5D,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC1E;AAEO,IAAMsG,oBAAmC,gBAC7CtG,CAAAA,UAAqE;AACpE,QAAMuG,eAAeC,QAAS;AAE9B,SAAO,MAAM;AACX,QAAID,aAAa/I,UAAUnD,aAAaoM,eAAeF,aAAa/I,UAAUnD,aAAaqM,OAAO;AAChG,aAAA7L,YAAAiK,yBAAA;QAAA,SAAuC9E,MAAM/B;QAAK,YAAY+B,MAAMd;QAAQ,cAAcc,MAAMtF;MAAU,GAAA,IAAA;IAC5G,OAAO;AACL,aAAAG,YAAAwL,yBAAA;QAAA,SAAuCrG,MAAM/B;QAAK,YAAY+B,MAAMd;QAAQ,cAAcc,MAAMtF;MAAU,GAAA,IAAA;IAC5G;EACD;AACH,GACA;EAAE8H,MAAM;EAAqBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC1E;ACvVO,IAAM2G,sBAAqC,gBAC/C3G,CAAAA,UAAqE;AACpE,QAAM6C,QAAQC,SAAU;AAExB,QAAM8D,SAASC,UAAW;AAE1B,QAAM5G,YAAYC,aAAc;AAEhC,QAAMgD,UAAUzC,SAAS,MAAMT,MAAMd,SAAS0B,iBAAiBZ,MAAM/B,KAAK,CAAC;AAE3E,QAAMkF,UAAU1C,SAAS,MAAMT,MAAMd,SAAS2B,kBAAkBb,MAAM/B,KAAK,CAAC;AAE5E,QAAM6I,gBAAgBrG,SACpB,MACEyC,QAAQ1F,MAAM9C,cACdkM,OAAOpJ,MAAMhD,SAASkG,UAAUC,OAChCiG,OAAOpJ,MAAM9C,eAAewI,QAAQ1F,MAAM9C,UAC9C;AAEA,QAAMqM,gBAAgBtG,SACpB,MACE0C,QAAQ3F,MAAM9C,cACdkM,OAAOpJ,MAAMhD,SAASkG,UAAUyD,OAChCyC,OAAOpJ,MAAM9C,eAAeyI,QAAQ3F,MAAM9C,UAC9C;AAEA,QAAM+P,YAAYhK,SAAS,MAAMyC,QAAQ1F,MAAM2D,YAAYgC,QAAQ3F,MAAM2D,QAAQ;AAEjF,QAAM2C,gBAAgBrD,SACpB,OAAO,CAAC,CAACqG,cAActJ,SAAS,CAAC,CAACuJ,cAAcvJ,UAAU,CAACiN,UAAUjN,SAAS,CAAC,CAACqF,MAAM+D,MACxF;AAEA,QAAMM,gBAAgBA,MAAMjH,UAAU,CAAA,CAAE;AAExC,SAAO,MAAM;;AACX,QAAI,CAAC6D,cAActG,MAAO,QAAO;AAEjC,UAAM8N,oBAAoBxE,cAActJ,SACpCqF,KAAAA,MAAM+D,WAAN/D,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUC;MAChBjG,YAAYwI,QAAQ1F,MAAM9C;MAC1ByM,SAASD;IACV,CAAA,IACD;AAEJ,UAAMqE,oBAAoBxE,cAAcvJ,SACpCqF,KAAAA,MAAM+D,WAAN/D,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUyD;MAChBzJ,YAAYyI,QAAQ3F,MAAM9C;MAC1ByM,SAASD;IACV,CAAA,IACD;AAEJ,WAAArM,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,SAAA;IAAA,GACxC4Q,CAAAA,oBAAiBzQ,YAAA,MAAA;MAAA,SAAA;MAAA,WACsC;IAAC,GAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;IACdyQ,GAAAA,CAAAA,iBAAiB,CAAA,CAAA,CAAA,IAAAzQ,YAAA,MAAA;MAAA,SAAA;MAAA,SAKjD;QAAEM,iBAAiB,OAAOoH,WAAW;MAAK;MAAA,WACxC;IAEZ,GAAA,IAAA,GACAgJ,oBAAiB1Q,YAAA,MAAA;MAAA,SAAA;MAAA,WAGL;MAAC,SACH;QAAEqQ,iBAAiB,OAAOhB,eAAe;QAAKiB,iBAAiB;MAAQ;IAAC,GAAA,CAAAtQ,YAAA,OAAA;MAAA,SAAA;IAExC0Q,GAAAA,CAAAA,iBAAiB,CAAA,CAAA,CAAA,IAAA1Q,YAAA,MAAA;MAAA,SAAA;MAAA,SAKjD;QACLM,iBAAiB,OAAOoH,WAAW;QACnC2I,iBAAiB,OAAOhB,eAAe;QACvCiB,iBAAiB;MAClB;MAAA,WACQ;IAAA,GAEZ,IAAA,CAAA,CAAA;EAGN;AACH,GACA;EAAE3I,MAAM;EAAuBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC5E;ACrFO,IAAMwL,oBAAmC,gBAC7CxL,CAAAA,UAAkC;AACjC,QAAMqH,QAAQ/F,IAAIgG,qBAAqBtH,MAAMd,QAAQ,CAAC;AAEtD,QAAMyJ,UAAUlI,SAAS,MAAMmI,KAAKC,IAAI7I,MAAMd,SAASmJ,iBAAiBrI,MAAMd,SAAS4J,cAAc,EAAEC,SAAQ,CAAE;AAEjH,QAAML,WAAWpH,IAA6B,IAAI;AAElD,QAAMiG,cAAc;IAAEU,SAASrJ;EAAoC;AAEnE,QAAMsJ,WAAY1N,CAAAA,SAAqB;AACrC,UAAMkN,MAAMgB,SAASlL;AAErB,QAAI,CAACkK,IAAK;AAEV,QAAI,CAAClN,MAAM;AACTkN,UAAI6B,cAAc;IACpB,OAAO;AACL,YAAMC,KAAK,YAAYxJ,MAAMd,SAASuK,MAAK,CAAE;AAC7C/B,UAAI6B,cAAc,IAAIC,EAAE,gBAAgB9I,UAAUlG,SAASkG,UAAUC,MAAMD,UAAUyD,MAAMzD,UAAUC,GAAG,CAAC;GAA6B6I,EAAE;GAAiDA,EAAE;GAA+CA,EAAE;IAC9O;EACD;AAED,QAAMhC,cAAeC,CAAAA,MAAkB;AACrC,QAAIC,MAAMD,EAAEE;AAGZ,QAAID,OAAOA,eAAeI,eAAeJ,IAAIE,aAAa,UAAU;AAClEC,yBAAoB;AACpB;IACF;AAEA,WAAOH,OAAOA,eAAeI,aAAa;AACxC,YAAMC,QAAQL,IAAIM,aAAa,YAAY;AAC3C,YAAMxN,OAAOkN,IAAIM,aAAa,WAAW;AACzC,UAAIxN,MAAM;AACR,YAAI+M,YAAYU,YAAYvH,UAAUlG,IAAI,GAAG;AAC3C+M,sBAAYU,UAAUvH,UAAUlG,IAAI;AACpC0N,mBAASxH,UAAUlG,IAAI,CAAC;AACxBqN,6BAAoB;QACtB;MACF;AACA,UAAIE,OAAO;AACT,YAAIA,UAAU,YAAYA,UAAU,UAAUA,UAAU,UAAU;AAChE,cAAIR,YAAYU,YAAYrJ,QAAW;AACrC2I,wBAAYU,UAAUrJ;AACtBsJ,qBAAStJ,MAAS;AAClBiJ,+BAAoB;UACtB;AACA;QACF,OAAO;AACL;QACF;MACF;AAEAH,YAAMA,IAAIS;IACZ;EACD;AAED,QAAMuB,WAAWC,YAAa;AAE9B,QAAMC,OAAOnJ,SAAS,OAAO;IAAEiJ,UAAUA,SAASlM,QAAQ;IAAMqM,YAAY;EAA6B,EAAE;AAE3GjI,uBAAqB5B,OAAQd,CAAAA,aAAa;AACxCmI,UAAM7J,QAAQ8J,qBAAqBpI,QAAQ;EAC7C,CAAC;AAED,QAAMpE,QAAQgP,aAAa;IAAE1D,MAAMuC;IAASiB;EAAK,CAAC;AAElD,QAAMG,gBAAgBtJ,SAAS,MAAMmI,KAAKC,IAAI,IAAI/N,MAAM0C,QAAQ,EAAE,CAAC;AAEnE,SAAO,MAAM;AACX,WAAA3C,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;MAAA,SAIa;QACL,CAACsH,kBAAkB,GAAG,GAAGyG,KAAKoB,MAAMD,cAAcvM,KAAK,CAAC;QACxDqM,YAAY;QACZH,UAAU,OAAO3O,gBAAgB;MACnC;IAAC,GAAA,CAAAF,YAAA,SAAA;MAAA,qBAAA;MAAA,OAE6B6N;IAAQ,GAAA,IAAA,GAAA7N,YAAA,SAAA;MAAA,SAAA;IAAA,GAAAA,CAAAA,YAAAA,YAAAA,MAAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;MAAA,SAGS+N,KAAKoB,MAAMD,cAAcvM,KAAK;IAAC,GAAA,IAAA,GAAA3C,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,IAAA,GAAAA,YAAA,OAAA;MAAA,SAAA;MAAA,SAE/B+N,KAAKoB,MAAMD,cAAcvM,KAAK;IAAC,GAAA,IAAA,GAAA3C,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAAA,YAAA,SAAA;MAAA,SAAA;IAAA,GAAAA,CAAAA,YAAAA,MAAAA,MAAAA,CAAAA,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,iBAAAA,CAAAA,CAAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,kBAAAA,CAAAA,CAAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,iBAAAA,CAAAA,CAAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,kBAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,GAAAT,YAAA,SAAA;MAAA,SAAA;MAAA,eAWlB2M;IAAAA,GACvDH,CAAAA,MAAM7J,MAAMM,IAAKsK,CAAAA,SAAIvN,YAAAuC,UAAA;MAAA,OACLgL,KAAKnK;IAAAA,GAAKpD,CAAAA,YAAAyL,mBAAA;MAAA,SACG8B,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAAkF,sBAAA;MAAA,SAC9DqI,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAA8L,qBAAA;MAAA,SAClEyB,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAA6H,qBAAA;MAAA,SACjE0F,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAEhG,GAACrE,YAAAyL,mBAAA;MAAA,SAEOtG,MAAMd,SAASmJ;MAAe,cACzBrI,MAAMd,SAASmJ;MAAe,YAChCrI,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAOrC;AACH,GACA;EAAEsD,MAAM;EAAqBxC,OAAO,CAAC,UAAU;AAAE,CACnD;AC/HO,IAAMyL,gBAA+B,gBACzCzL,CAAAA,UAAkC;AACjC,QAAMlE,aAAa4P,cAAe;AAElC,SAAO,MAAM;AACX,WAAO5P,WAAW0B,QAAK3C,YAAA2Q,mBAAA;MAAA,YACQxL,MAAMd;IAAAA,GAAQrE,IAAAA,IAAAA,YAAAyN,qBAAA;MAAA,YAEZtI,MAAMd;IACtC,GAAA,IAAA;EACF;AACH,GACA;EAAEsD,MAAM;EAAiBxC,OAAO,CAAC,UAAU;AAAE,CAC/C;ACAA,IAAM2L,qBAAqBA,CAAC;EAC1B1N;EACAtC;EACAD;EACAG;EACA6B;EACAhD;EACAwE;EACApD;EACAqE;EACAhB;EACAvE;EACA0F;AAcF,MAAM;AACJ,SAAAzF,YAAA,MAAA;IAAA,aACiBoD;IAAK,cAAA;IAAA,SAAA;EAAA,GAAA,CAAApD,YAAA,MAAA;IAAA,SAAA;IAAA,SAGT;MACLI,OAAO,OAAOgH,wBAAwB;MACtC9G,iBAAiB,OAAOyQ,mBAAmB;MAC3C9Q,OAAO,iBAAiBqH,kBAAkB;MAC1CE,UAAU,iBAAiBF,kBAAkB;MAC7CC,UAAU,iBAAiBD,kBAAkB;IAC/C;EAAC,GAAA,CAEAhC,mBAAetF,YAAAU,sBAAA;IAAA,SAEL0C,QAAQ;IAAC,cACJvD;IAAU,YACZwE;IAAQ,QACZwB,UAAUC;IAAG,iBACJL;IAAgB,mBACd1F;EAAe,GAAA,IAAA,GAEnCC,YAAA,OAAA;IAAA,SAAA;EAAA,GAAA,CAAAA,YAAA,QAAA;IAAA,qBAE0BH;IAAU,SAAA;EAChCA,GAAAA,CAAAA,UAAU,CAAA,GAAAG,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,IAAA,GAAAA,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,GAAAA,YAAA,MAAA;IAAA,SAAA;IAAA,SAMwC;MAAEM,iBAAiB,OAAO0Q,gBAAgB;IAAI;EAAA,GAAChR,CAAAA,YAAAoE,aAAA;IAAA,cAExFnD;IAAU,YACZoD;IAAQ,mBACDC;IAAe,WACvBzD;IAAO,YACNC;IAAQ,aACPE;IAAS,cACR6B;EAAU,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAKhC;AAEA,IAAMoO,qBAAqBA,CAAC;EAC1B7N;EACAtC;EACAD;EACAG;EACA6B;EACAhD;EACAwE;EACApD;EACAqE;EACAhB;EACAvE;EACA0F;AAcF,MAAM;AACJ,SAAAzF,YAAA,MAAA;IAAA,aACiBoD;IAAK,cAAA;IAAA,SAAA;EAAA,GAAA,CAAApD,YAAA,MAAA;IAAA,SAAA;IAAA,SAGT;MACLI,OAAO,OAAOgH,wBAAwB;MACtC9G,iBAAiB,OAAO4Q,mBAAmB;MAC3CjR,OAAO,iBAAiBqH,kBAAkB;MAC1CE,UAAU,iBAAiBF,kBAAkB;MAC7CC,UAAU,iBAAiBD,kBAAkB;IAC/C;EAAC,GAAA,CAEAhC,mBAAetF,YAAAU,sBAAA;IAAA,SAEL0C,QAAQ;IAAC,cACJvD;IAAU,YACZwE;IAAQ,QACZwB,UAAUyD;IAAG,iBACJ7D;IAAgB,mBACd1F;EAAe,GAAA,IAAA,GAEnCC,YAAA,OAAA;IAAA,SAAA;EAAA,GAAA,CAAAA,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,IAAA,GAAAA,YAAA,QAAA;IAAA,SAAA;EAAA,GAAA,IAAA,GAAAA,YAAA,QAAA;IAAA,qBAI0BH;IAAU,SAAA;EAAA,GAChCA,CAAAA,UAAU,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAG,YAAA,MAAA;IAAA,SAAA;IAAA,SAIwC;MAAEM,iBAAiB,OAAO6Q,gBAAgB;IAAI;EAAA,GAACnR,CAAAA,YAAAoE,aAAA;IAAA,cAExFnD;IAAU,YACZoD;IAAQ,mBACDC;IAAe,WACvBzD;IAAO,YACNC;IAAQ,aACPE;IAAS,cACR6B;EAAU,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAKhC;AAEO,IAAMuO,yBAAwC,gBAClDjM,CAAAA,UAAqE;;AACpE,QAAMkM,cAAczL,SAAS,MAAMT,MAAMd,SAASiN,eAAenM,MAAM/B,KAAK,CAAC;AAE7E,QAAMnC,aAAa4P,cAAe;AAElC,QAAMzL,YAAYC,aAAc;AAEhC,QAAMI,mBAAmBC,oBAAqB;AAE9C,QAAMpB,kBAAkBkB,mBAAoB;AAE5C,QAAMF,kBAAkBC,mBAAoB;AAE5C,QAAMgM,uBAAuB3L,SAAS,MAAMyL;;AAAAA,YAAAA,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmB/K;EAAAA,CAAQ;AAEvE,QAAMkL,uBAAuB5L,SAAS,MAAMQ;;AAAAA,WAAAA,4BAA2BiL,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmBnL,IAAI;EAAA,CAAC;AAE/F,QAAMM,oBAAoBC,MACxB4K,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBI,iBACftM,MAAMd,SAASsC,iBAAiB0K,YAAY1O,MAAM8O,aAAa,MAC/DJ,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBK,iBACjBvM,MAAMd,SAASqC,iBAAiB2K,YAAY1O,MAAM+O,aAAa,IAC/D3N,MACR;AAEA,QAAM6C,mBAAmBH,MACvB4K,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBI,iBACftM,MAAMd,SAASyC,gBAAgBuK,YAAY1O,MAAM8O,aAAa,MAC9DJ,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBK,iBACjBvM,MAAMd,SAASwC,gBAAgBwK,YAAY1O,MAAM+O,aAAa,IAC9D3N,MACR;AAEAgD,uBAAqB5B,OAAQd,CAAAA,aAAa;;AACxCmC,sBAAkB7D,UAAQ0O,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmBI,iBACzCpN,SAASsC,iBAAiB0K,YAAY1O,MAAM8O,aAAa,MACzDJ,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmBK,iBACjBrN,SAASqC,iBAAiB2K,YAAY1O,MAAM+O,aAAa,IACzD3N;AAEN6C,qBAAiBjE,UAAQ0O,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmBI,iBACxCpN,SAASyC,gBAAgBuK,YAAY1O,MAAM8O,aAAa,MACxDJ,MAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,IAAmBK,iBACjBrN,SAASwC,gBAAgBwK,YAAY1O,MAAM+O,aAAa,IACxD3N;EACR,CAAC;AAED,QAAMhE,kBAAkBA,CAACF,YAAoBF,SAAoByF,UAAU;IAAEzF;IAAYE;EAAuB,CAAC;AAEjH,SAAO,MAAM;AACX,QAAI0R,qBAAqB5O,MAAO,QAAO;AAEvC,QAAI6O,qBAAqB7O,OAAO;AAC9B,UAAI0O,YAAY1O,MAAM+O,eAAe;AACnC,eAAA1R,YAAA8Q,oBAAA;UAAA,SAEW3L,MAAMtF;UAAU,cACXoB,WAAW0B;UAAK,YAClBwC,MAAMd;UAAQ,WACfgN,YAAY1O,MAAMA,SAAS;UAAE,YAC5B0O,YAAY1O,MAAMuD;UAAI,aACrBU,iBAAiBjE;UAAK,cACrB6D,kBAAkB7D;UAAK,mBAClB2B,gBAAgB3B;UAAK,mBACrB2C,gBAAgB3C;UAAK,cAC1B0O,YAAY1O,MAAM+O;UAAa,oBACzBjM;UAAgB,mBACjB1F;QAAe,GAAA,IAAA;MAGtC,OAAO;AACL,eAAAC,YAAAiR,oBAAA;UAAA,SAEW9L,MAAMtF;UAAU,cACXoB,WAAW0B;UAAK,YAClBwC,MAAMd;UAAQ,WACfgN,YAAY1O,MAAMA,SAAS;UAAE,YAC5B0O,YAAY1O,MAAMuD;UAAI,aACrBU,iBAAiBjE;UAAK,cACrB6D,kBAAkB7D;UAAK,mBAClB2B,gBAAgB3B;UAAK,mBACrB2C,gBAAgB3C;UAAK,cAC1B0O,YAAY1O,MAAM8O;UAAa,oBACzBhM;UAAgB,mBACjB1F;QAAe,GAAA,IAAA;MAGtC;IACF,OAAO;AACL,aAAAC,YAAA,MAAA;QAAA,aAEemF,MAAMtF;QAAU,cACfwR,YAAY1O,MAAMuD,OAAO,SAAS;QAAO,SAAA;MAAA,GAAA,CAAAlG,YAAA,MAAA;QAAA,SAAA;QAAA,SAK5C;UACLI,OAAO,OAAOiR,YAAY1O,MAAMuD,OAAOkB,2BAA2BC,yBAAyB;UAC3F/G,iBAAiB+Q,YAAY1O,MAAMuD,OAC/B,OAAOyL,qBAAqB,MAC5B,OAAOC,mBAAmB;UAC9B3R,OAAO,iBAAiBqH,kBAAkB;UAC1CE,UAAU,iBAAiBF,kBAAkB;UAC7CC,UAAU,iBAAiBD,kBAAkB;QAC/C;MAAC,GAAA,CAEAhC,gBAAgB3C,SAAS0O,YAAY1O,MAAMuD,QAAIlG,YAAAU,sBAAA;QAAA,SAErCyE,MAAM/B;QAAK,YACR+B,MAAMd;QAAQ,cACZgN,YAAY1O,MAAM8O;QAAa,QACrC5L,UAAUyD;QAAG,mBACFvJ;QAAe,iBACjB0F;MAAgB,GAAA,IAAA,GAElCzF,YAAA,OAAA;QAAA,SAAA;MAAA,GAAA,CAAAA,YAAA,QAAA;QAAA,qBAE0BqR,YAAY1O,MAAM+O;QAAa,SAAA;MAAA,GAAA,CACrDL,YAAY1O,MAAM+O,aAAa,CAAA,GAAA1R,YAAA,QAAA;QAAA,SAAA;MAAA,GAAA,IAAA,GAAAA,YAAA,QAAA;QAAA,qBAGTqR,YAAY1O,MAAM8O;QAAa,SAAA;MAAA,GAAA,CACrDJ,YAAY1O,MAAM8O,aAAa,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAzR,YAAA,MAAA;QAAA,SAAA;QAAA,SAM7B;UACLM,iBAAiB+Q,YAAY1O,MAAMuD,OAAO,OAAO2L,kBAAkB,MAAM,OAAOD,mBAAmB;QACrG;MAAA,GAAC5R,CAAAA,YAAAoE,aAAA;QAAA,cAGanD,WAAW0B;QAAK,YAClBwC,MAAMd;QAAQ,mBACPC,gBAAgB3B;QAAK,WAC7B0O,YAAY1O,MAAMA,SAAS;QAAE,YAC5B0O,YAAY1O,MAAMuD;QAAI,aACrBU,iBAAiBjE;QAAK,cACrB6D,kBAAkB7D;MAAK,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IAK7C;EACD;AACH,GACA;EAAEgF,MAAM;EAA0BxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC/E;ACpTO,IAAM2M,wBAAuC,gBACjD3M,CAAAA,UAAqE;AACpE,QAAM2C,aAAaC,cAAe;AAElC,QAAMC,QAAQC,SAAU;AAExB,QAAMoJ,cAAczL,SAAS,MAAMT,MAAMd,SAASiN,eAAenM,MAAM/B,KAAK,CAAC;AAE7E,QAAM2O,YAAYnM,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBY,YAAlBZ,OAAAA,SAAAA,GAA4BuJ,YAAY1O,MAAM+O,aAAAA;EAAAA,CAAc;AAE7F,QAAMM,YAAYpM,SAAS,MAAA;;AAAMkC,YAAAA,MAAAA,KAAAA,WAAWnF,UAAXmF,OAAAA,SAAAA,GAAkBc,YAAlBd,OAAAA,SAAAA,GAA4BuJ,YAAY1O,MAAM8O,aAAAA;EAAAA,CAAc;AAE7F,QAAM3I,kBAAkBlD,SAAS,MAAMyL,YAAY1O,MAAM2D,QAAQ;AAEjE,QAAM2C,gBAAgBrD,SAAS,MAC7BsD,SAAS6I,UAAUpP,SAASqP,UAAUrP,UAAU,CAACmG,gBAAgBnG,SAASqF,MAAMmB,MAAM,CACxF;AAEA,QAAMlJ,QAAQyJ,YAAY;IACxBF,UAAU/C,IAAI,6BAA6B;IAC3CgD,QAAQR;EACV,CAAC;AAED,SAAO,MAAM;AACX,QAAI,CAACA,cAActG,MAAO,QAAO;AAEjC,WAAA3C,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,SAAA;IAAA,GAAA,CAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,WACmB;IAAC,GAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;MAAA,SACM;QAAEC,OAAOA,MAAM0C,QAAQ;MAAK;IAAC,GAAA,CAC3F1C,MAAM0C,QAAQ,KACboP,UAAUpP,SACVqF,MAAMmB,OAAO;MACX9E,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUC;MAChBjG,YAAYwR,YAAY1O,MAAM+O;MAC9B/H,MAAMoI,UAAUpP,MAAMgH;MACtBC,UAAUzE,MAAMd,SAASwF;IAC3B,CAAC,GACF5J,MAAM0C,QAAQ,KACbqP,UAAUrP,SACVqF,MAAMmB,OAAO;MACX9E,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUyD;MAChBzJ,YAAYwR,YAAY1O,MAAM8O;MAC9B9H,MAAMqI,UAAUrP,MAAMgH;MACtBC,UAAUzE,MAAMd,SAASwF;IAC3B,CAAC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA;EAKb;AACH,GACA;EAAElC,MAAM;EAAyBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC9E;AC9CO,IAAM8M,sBAAqC,gBAC/C9M,CAAAA,UAAqE;AACpE,QAAM+E,cAActE,SAAS,MAAMT,MAAMd,SAAS6N,mBAAmB/M,MAAM/B,KAAK,CAAC;AAEjF,QAAMmF,eAAe3C,SAAS,MAAMT,MAAMd,SAASmE,iBAAgB,CAAE;AAErE,QAAM4B,cAAcxE,SAAS,MAAM2C,aAAa5F,SAASuH,YAAYvH,SAASuH,YAAYvH,MAAMwP,WAAW;AAE3G,QAAMlR,aAAa4P,cAAe;AAElC,QAAM5H,gBAAgBxC,IACpByD,YAAYvH,SACVuH,YAAYvH,MAAMwP,eAClBjI,YAAYvH,MAAMwP,YAAYpH,mBAAmBb,YAAYvH,MAAMwP,YAAYrH,cACnF;AAEA,QAAMsH,qBAAqB3L,IACzByD,YAAYvH,SACVuH,YAAYvH,MAAMwP,eAClBjI,YAAYvH,MAAMwP,YAAYrH,iBAAiBZ,YAAYvH,MAAMwP,YAAYpH,mBAAmBC,UACpG;AAEA,QAAMR,qBAAqB5E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAM8H,OAAO;AAExF,QAAMC,oBAAoB9E,SAAS,MAAMsE,YAAYvH,SAASuH,YAAYvH,MAAMV,MAAM;AAEtF,QAAM0I,oBAAoB/E,SACxB,MAAMsE,YAAYvH,SAASwC,MAAMd,SAASuG,qBAAoB,KAAM,CAACV,YAAYvH,MAAMwP,WACzF;AAEApL,uBAAqB5B,OAAO,MAAM;AAChC8D,kBAActG,QACZuH,YAAYvH,SACZuH,YAAYvH,MAAMwP,eAClBjI,YAAYvH,MAAMwP,YAAYpH,mBAAmBb,YAAYvH,MAAMwP,YAAYrH;AAEjFsH,uBAAmBzP,QACjBuH,YAAYvH,SACZuH,YAAYvH,MAAMwP,eAClBjI,YAAYvH,MAAMwP,YAAYrH,iBAAiBZ,YAAYvH,MAAMwP,YAAYpH,mBAAmBC;EACpG,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAAC/B,cAActG,SAAS,CAACgI,kBAAkBhI,MAAO,QAAO;AAE7D,WAAA3C,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAO,cAAA;MAAA,SAAA;IAAA,GAAA,CAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,SAG9B;QACLM,iBAAiB,OAAO6K,oBAAoB;QAC5C/K,OAAO,OAAOgH,wBAAwB;QACtCnH,OAAO,iBAAiBqH,kBAAkB;QAC1CE,UAAU,iBAAiBF,kBAAkB;QAC7CC,UAAU,iBAAiBD,kBAAkB;MAC/C;IAAA,GAEC8C,CAAAA,YAAYzH,QACX6H,mBAAmB7H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKXQ,MAAM2E,MAAMd,SAASgO,oBAAoB,MAAMlN,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAIpEW,kBAAkB/H,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKdQ,MAAM2E,MAAMd,SAASgO,oBAAoB,QAAQlN,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAItEsI,mBAAmBzP,QAAK3C,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAKfQ,MAAM2E,MAAMd,SAASgO,oBAAoB,OAAOlN,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAAgK,WAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,IAAAhK,YAAAuC,UAAA,MAAA,CAAAvC,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAU1DQ,MAAM2E,MAAMd,SAASgO,oBAAoB,QAAQlN,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA8J,YAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAA9J,YAAA,UAAA;MAAA,SAAA;MAAA,SAAA;MAAA,cAAA;MAAA,WAQ7DQ,MAAM2E,MAAMd,SAASgO,oBAAoB,MAAMlN,MAAM/B,KAAK;IAAA,GAACpD,CAAAA,YAAA+J,UAAA;MAAA,aAAA;IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAKzE/J,YAAA,OAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAGF,GAAA,CAAA,CAAA,CAAA,CAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;MAAA,SAIM;QAAEM,iBAAiB,OAAO4K,iBAAiB;MAAI;IAAC,GAAA,CAAAlL,YAAA,OAAA;MAAA,SAAA;MAAA,SAI9C;QACLgF,YAAY/D,WAAW0B,QAAQ,aAAa;QAC5CsC,WAAWhE,WAAW0B,QAAQ,cAAc;QAC5CvC,OAAO,OAAOiL,oBAAoB;MACpC;IAAC,GAAA,GAEAnB,KAAAA,YAAYvH,MAAMwP,gBAAlBjI,OAAAA,SAAAA,GAA+BoB,cAAapB,YAAYvH,MAAM4I,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA;EAK5E;AACH,GACA;EAAE5D,MAAM;EAAuBxC,OAAO,CAAC,SAAS,YAAY,YAAY;AAAE,CAC5E;AC1IO,IAAMmN,wBAAuC,gBACjDnN,CAAAA,UAAqE;AACpE,QAAM6C,QAAQC,SAAU;AAExB,QAAM8D,SAASC,UAAW;AAE1B,QAAM5G,YAAYC,aAAc;AAEhC,QAAMgM,cAAczL,SAAS,MAAMT,MAAMd,SAASiN,eAAenM,MAAM/B,KAAK,CAAC;AAE7E,QAAMmP,YAAY3M,SAChB,MACEyL;;AAAAA,aAAAA,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBK,kBACnB3F,OAAOpJ,MAAMhD,SAASkG,UAAUC,OAChCiG,OAAOpJ,MAAM9C,eAAewR,YAAY1O,MAAM+O;EAAAA,CAClD;AAEA,QAAMc,YAAY5M,SAChB,MACEyL;;AAAAA,aAAAA,KAAAA,YAAY1O,UAAZ0O,OAAAA,SAAAA,GAAmBI,kBACnB1F,OAAOpJ,MAAMhD,SAASkG,UAAUyD,OAChCyC,OAAOpJ,MAAM9C,eAAewR,YAAY1O,MAAM8O;EAAAA,CAClD;AAEA,QAAM3I,kBAAkBlD,SAAS,MAAMyL,YAAY1O,MAAM2D,QAAQ;AAEjE,QAAM2C,gBAAgBrD,SACpB,OAAO2M,UAAU5P,SAAS6P,UAAU7P,UAAU,CAACmG,gBAAgBnG,SAAS,CAAC,CAACqF,MAAM+D,MAClF;AAEA,QAAMM,gBAAgBA,MAAMjH,UAAU,CAAA,CAAE;AAExC,QAAMnF,QAAQyJ,YAAY;IACxBF,UAAU/C,IAAI,6BAA6B;IAC3CgD,QAAQR;EACV,CAAC;AAED,SAAO,MAAM;;AACX,QAAI,CAACA,cAActG,MAAO,QAAO;AAEjC,WAAA3C,YAAA,MAAA;MAAA,aACiB,GAAGmF,MAAMtF,UAAU;MAAS,cAAA;MAAA,SAAA;IAAA,GAAA,CAAAG,YAAA,MAAA;MAAA,SAAA;MAAA,WACS;IAAC,GAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;MAAA,SACgB;QAAEC,OAAOA,MAAM0C,QAAQ;MAAK;IAAC,GAAA,CAC3F1C,MAAM0C,QAAQ,KACb4P,UAAU5P,WACVqF,KAAAA,MAAM+D,WAAN/D,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUC;MAChBjG,YAAYwR,YAAY1O,MAAM+O;MAC9BpF,SAASD;IACX,CAAA,IACDpM,MAAM0C,QAAQ,KACb6P,UAAU7P,WACVqF,KAAAA,MAAM+D,WAAN/D,OAAAA,SAAAA,GAAAA,KAAAA,OAAe;MACb3D,UAAUc,MAAMd;MAChB1E,MAAMkG,UAAUyD;MAChBzJ,YAAYwR,YAAY1O,MAAM8O;MAC9BnF,SAASD;IACX,CAAA,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA;EAKb;AACH,GACA;EAAE1E,MAAM;EAAyBxC,OAAO,CAAC,YAAY,SAAS,YAAY;AAAE,CAC9E;AC5DO,IAAMsN,kBAAiC,gBAC3CtN,CAAAA,UAAkC;AACjC,QAAMqH,QAAQ/F,IAAIiM,sBAAsBvN,MAAMd,QAAQ,CAAC;AAEvD,QAAMyJ,UAAUlI,SAAS,MACvBmI,KAAKC,IAAI7I,MAAMd,SAASsO,mBAAmBxN,MAAMd,SAAS4J,cAAc,EAAEC,SAAQ,CACpF;AAEAnH,uBAAqB5B,OAAQd,CAAAA,aAAa;AACxCmI,UAAM7J,QAAQ+P,sBAAsBrO,QAAQ;EAC9C,CAAC;AAED,QAAMwK,WAAWC,YAAa;AAE9B,QAAM7N,aAAa4P,cAAe;AAElC,QAAMnE,cAAc;IAAEU,SAASrJ;EAAkC;AAEjE,QAAM8J,WAAWpH,IAA6B,IAAI;AAElD,QAAM4G,WAAYH,CAAAA,UAAoB;AACpC,UAAML,MAAMgB,SAASlL;AAErB,QAAI,CAACkK,IAAK;AAEV,QAAIK,UAAUnJ,QAAW;AACvB8I,UAAI6B,cAAc;IACpB,OAAO;AACL,YAAMC,KAAK,YAAYxJ,MAAMd,SAASuK,MAAK,CAAE;AAC7C/B,UAAI6B,cAAc,IAAIC,EAAE;GAAiDA,EAAE;GAA+CA,EAAE;IAC9H;EACD;AAED,QAAMhC,cAAeC,CAAAA,MAAkB;AACrC,QAAIC,MAAMD,EAAEE;AAEZ,QAAID,QAAOA,OAAAA,OAAAA,SAAAA,IAAKE,cAAa,UAAU;AACrCC,yBAAoB;AACpB;IACF;AAEA,WAAOH,OAAOA,eAAeI,aAAa;AACxC,YAAMC,QAAQL,IAAIM,aAAa,YAAY;AAC3C,UAAID,OAAO;AACT,YAAIA,UAAU,YAAYA,UAAU,UAAUA,UAAU,UAAU;AAChE,cAAIR,YAAYU,YAAY,OAAO;AACjCV,wBAAYU,UAAU;AACtBC,qBAAS,KAAK;AACdL,+BAAoB;UACtB;QACF,OAAO;AACL,cAAIN,YAAYU,YAAY,MAAM;AAChCV,wBAAYU,UAAU;AACtBC,qBAAS,IAAI;AACbL,+BAAoB;UACtB;QACF;AACA;MACF;AACAH,YAAMA,IAAIS;IACZ;EACD;AAED,QAAMyB,OAAOnJ,SAAS,OAAO;IAAEiJ,UAAUA,SAASlM,QAAQ;IAAMqM,YAAY;EAA6B,EAAE;AAE3G,QAAM/O,QAAQgP,aAAa;IAAE1D,MAAMuC;IAASiB;EAAK,CAAC;AAElD,QAAMG,gBAAgBtJ,SAAS,MAAMmI,KAAKC,IAAI,IAAI/N,MAAM0C,QAAQ,EAAE,CAAC;AAEnE,SAAO,MAAM;AACX,WAAA3C,YAAA,OAAA;MAAA,SAEW,qBAAqBiB,WAAW0B,QAAQ,2BAA2B,0BAA0B;IAAS,GAAA,CAAA3C,YAAA,SAAA;MAAA,qBAAA;MAAA,OAE/E6N;IAAQ,GAAA,IAAA,GAAA7N,YAAA,OAAA;MAAA,SAAA;MAAA,SAG7B;QACL,CAACsH,kBAAkB,GAAG,GAAGyG,KAAKoB,MAAMD,cAAcvM,KAAK,CAAC;QACxDqM,YAAY;QACZH,UAAU,OAAO3O,gBAAgB;MACnC;IAAC,GAAA,CAAAF,YAAA,SAAA;MAAA,SAGQ,8DAA8DiB,WAAW0B,QAAQ,gBAAgB,EAAE;IAAA,GAAE3C,CAAAA,YAAAA,YAAAA,MAAAA,CAAAA,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,IAAA,GAAAA,YAAA,OAAA;MAAA,SAAA;IAAA,GAAA,IAAA,CAAA,CAAA,GAAAA,YAAA,SAAA;MAAA,SAAA;IAAA,GAAAA,CAAAA,YAAAA,MAAAA,MAAAA,CAAAA,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,aAAAA,CAAAA,CAAAA,GAAAT,YAAA,MAAA;MAAA,SAAA;IAAA,GAAAS,CAAAA,gBAAAA,cAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,GAAAT,YAAA,SAAA;MAAA,SAAA;MAAA,eAYlD2M;IAAAA,GACvDH,CAAAA,MAAM7J,MAAMM,IAAKsK,CAAAA,SAAIvN,YAAAuC,UAAA;MAAA,OACLgL,KAAKnK;IAAAA,GAAKpD,CAAAA,YAAAiS,qBAAA;MAAA,SACK1E,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAAoR,wBAAA;MAAA,SAC9D7D,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAAsS,uBAAA;MAAA,SAClE/E,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAAA,GAAQrE,IAAAA,GAAAA,YAAA8R,uBAAA;MAAA,SACjEvE,KAAKnK;MAAK,cAAcmK,KAAK1N;MAAU,YAAYsF,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAElG,GAACrE,YAAAiS,qBAAA;MAAA,SAEO9M,MAAMd,SAASsO;MAAiB,cAC3BxN,MAAMd,SAASsO;MAAiB,YAClCxN,MAAMd;IAAQ,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAOrC;AACH,GACA;EAAEc,OAAO,CAAC,UAAU;EAAGwC,MAAM;AAAkB,CACjD;AC1GAiL,UAAUjL,OAAO;AA8BV,IAAMkL,WAAWC,gBAMtB,CAAC3N,OAAO4N,YAAY;;AAClB,QAAMC,cAAcA,MAAM;;AACxB,QAAI7N,MAAMd,UAAU;AAClB,YAAMA,YAAW4O,SAASC,eAAe,CAAA,CAAE;AAC3C7O,gBAAS8O,iBAAiBhO,MAAMd,SAAS+O,eAAAA,CAAgB;AAClD/O,aAAAA;IAAAA;AAET,QAAIc,MAAMwE,KACR,QAAO,IAAIsJ,WACT9N,MAAAA,MAAMwE,KAAKjB,YAAXvD,OAAAA,SAAAA,IAAoBkO,aAAY,MAChClO,MAAAA,MAAMwE,KAAKjB,YAAXvD,OAAAA,SAAAA,IAAoBmO,YAAW,MAC/BnO,KAAAA,MAAMwE,KAAKf,YAAXzD,OAAAA,SAAAA,GAAoBkO,aAAY,MAChClO,KAAAA,MAAMwE,KAAKf,YAAXzD,OAAAA,SAAAA,GAAoBmO,YAAW,IAC/BnO,MAAMwE,KAAK4J,SAAS,CAAA,KACpBpO,KAAAA,MAAMwE,KAAKjB,YAAXvD,OAAAA,SAAAA,GAAoBqO,aAAY,MAChCrO,KAAAA,MAAMwE,KAAKf,YAAXzD,OAAAA,SAAAA,GAAoBqO,aAAY,EAClC;AACK,WAAA;EACT;AAEMnP,QAAAA,WAAWoC,IAAcuM,YAAAA,CAAa;AAE5C,QAAMrE,KAAKlI,KAAIpC,MAAAA,KAAAA,SAAS1B,UAAT0B,OAAAA,SAAAA,GAAgBuK,UAAhBvK,OAAAA,SAAAA,GAAAA,KAAAA,EAAAA,CAAyB;AAElCoP,QAAAA,cAAchN,IAA+C,CAAA,CAAE;AAErE,QAAMiN,aAAajN,IAAoB;AAEvC,QAAMrB,YAAauO,CAAiD,MAAA;AAClE,QAAI,OAAOZ,QAAQ/K,MAAM+D,WAAW,YAAY;AAC9C0H,kBAAY9Q,QAAQgR;IAAAA;EAExB;AAEA,QAAMrP,kBAAkBsB,SAAS,MAAMT,MAAMyO,qBAAqB,IAAI;AAEtE,QAAMC,QAAQjO,SAAS,MAAMT,MAAM2O,aAAa;AAG9C,QAAA,MAAM3O,MAAMd,UACZ,MAAM;;AACJA,KAAAA,OAAAA,MAAAA,SAAS1B,UAAT0B,OAAAA,SAAAA,IAAgB0P,UAAhB1P,OAAAA,SAAAA,IAAAA,KAAAA,GAAAA;AACAA,aAAS1B,QAAQqQ,YAAY;EAAA,GAE/B;IAAEgB,WAAW;EAAA,CACf;AAGE,QAAA,MAAM7O,MAAM8O,oBACZ,MAAM;AACJ,QAAI9O,MAAM8O,oBAAoB;AAC5BR,kBAAY9Q,QAAQ;QAClBhD,MAAMwF,MAAM8O,mBAAmBtU;QAC/BE,YAAYsF,MAAM8O,mBAAmBpU;MACvC;IAAA;EACF,GAEF;IAAEmU,WAAW;IAAME,MAAM;EAAA,CAC3B;AAGE,QAAA,MAAM/O,MAAMwE,MACZ,MAAM;;AACJtF,KAAAA,OAAAA,MAAAA,SAAS1B,UAAT0B,OAAAA,SAAAA,IAAgB0P,UAAhB1P,OAAAA,SAAAA,IAAAA,KAAAA,GAAAA;AACAA,aAAS1B,QAAQqQ,YAAY;EAAA,GAE/B;IAAEgB,WAAW;IAAME,MAAM;EAAA,CAC3B;AAEAC,QACE,MAAM9P,SAAS1B,OACf,MAAO8Q,YAAY9Q,QAAQ,CAAA,CAC7B;AAEA,QAAM+K,YAAYjO,aAAa;AAE/B,QAAM2U,gBAAiBhG,CAAsC,YAAA;AACvD,QAAA,CAACV,UAAU/K,SAAS,CAAC0B,SAAS1B,SAAS,CAACwC,MAAMd,SAAU;AAC5D,UAAMhF,YAAWgF,SAAS1B;AACpB0B,UAAAA,SAASgQ,mBAAmBhV,SAAQ;AAC1C+O,YAAQ,MAAMjJ,MAAMd,SAASiQ,mBAAmBjV,SAAQ,CAAC;EAC3D;AAEA,QAAMkV,WAAWA,MAAM;AACrB,QAAI,CAAC7G,UAAU/K,SAAS,CAAC0B,SAAS1B,MAAO;AACzC,UAAMtD,YAAWgF,SAAS1B;AACjB6R,cAAAA,UAAUX,MAAMlR,KAAK;AAC9BtD,cAASoV,QAAQ;AACjBpV,cAASqV,oBAAoB;AAC7BrV,cAASsV,sBAAsB;EACjC;AAEA,QAAMC,aAAaA,MAAM;AACnB,QAAA,CAAClH,UAAU/K,SAAS,CAAC2B,gBAAgB3B,SAAS,CAAC0B,SAAS1B,MAAO;AAEnE,UAAMtD,YAAWgF,SAAS1B;AAC1BtD,cAASuV,WAAW;MAClBC,qBAAqB1P,MAAM0P;IAAAA,CAC5B;AACDxV,cAASwK,UAAU;EACrB;AAEA,QAAMiL,gBAAiB1G,CAAsC,YAAA;AACvD,QAAA,CAACV,UAAU/K,SAAS,CAAC0B,SAAS1B,SAAS,CAAC+Q,WAAW/Q,MAAO;AAC9D,UAAMtD,YAAWgF,SAAS1B;AAGpBA,UAAAA;AACN,UAAMoS,OAAOA,MAAM;;AACjBrB,OAAAA,MAAAA,WAAW/Q,UAAX+Q,OAAAA,SAAAA,IAAkBsB,aAAa,cAAc3V,UAAS4V,UAAAA,KAAe,OAAA;AACrEvB,OAAAA,MAAAA,WAAW/Q,UAAX+Q,OAAAA,SAAAA,IAAkBsB,aAAa,oBAAoB3V,UAAS6V,oBAAAA,CAAAA;IAC9D;AAEK,SAAA;AAECtN,UAAAA,KAAKvI,UAAS8V,UAAUJ,IAAI;AAE1B,YAAA,MAAMnN,GAAAA,CAAI;EACpB;AAEA,QAAMwN,SAAUhH,CAAsC,YAAA;AAChD,QAAA,CAAC/J,SAAS1B,MAAO;AACrB,UAAMtD,YAAWgF,SAAS1B;AACvBA,OAAAA,QAAQtD,UAASuP,MAAM;AAClB,YAAA,MAAMvP,UAASgW,QAAAA,CAAS;EAClC;AAEajH,cAAAA,CAAAA,YAAYgG,cAAchG,OAAO,CAAC;AAEnC,cAAA,MAAMmG,SAAAA,CAAU;AAEhB,cAAA,MAAMK,WAAAA,CAAY;AAEjBxG,cAAAA,CAAAA,YAAYgH,OAAOhH,OAAO,CAAC;AAE3BA,cAAAA,CAAAA,YAAY0G,cAAc1G,OAAO,CAAC;AAE/CkH,UAAQC,UAAU5G,EAAE;AAEpB2G,UAAQE,eAAe9H,SAAS;AAExB+H,UAAAA,aAAa1C,QAAQ/K,KAAK;AAE1B0N,UAAAA,wBAAwB3C,QAAQ4C,IAAI;AAE5CL,UAAQM,mBAAmBnC,WAAW;AAEtC6B,UAAQO,sBAAsBzQ,SAAS;AAE5BD,aAAAA,OAAO,gBAAgB2Q,YAAY;IAAEC,cAAcvW,aAAaoM;EAAAA,CAAa;AAE7EzG,aAAAA,OAAO,oBAAoB6Q,gBAAgB;IAAED,cAAc;EAAA,CAAI;AAE/D5Q,aAAAA,OAAO,gBAAgB8Q,gBAAgB;AAEvC9Q,aAAAA,OAAO,qBAAqB+Q,qBAAqB;AAEjD/Q,aAAAA,OAAO,qBAAqBgR,qBAAqB;AAEjDhR,aAAAA,OAAO,cAAciR,kBAAkB;IAAEC,WAAW;EAAA,CAAM;AAErEC,cAAY,MAAMjS;;AAAAA,YAAAA,OAAAA,MAAAA,SAAS1B,UAAT0B,OAAAA,SAAAA,IAAgB0P,UAAhB1P,OAAAA,SAAAA,IAAAA,KAAAA,GAAAA;EAAAA,CAAyB;AAE3C0O,UAAQwD,OAAO;IAAEC,qBAAqBA,MAAMnS,SAAS1B;EAAAA,CAAO;AAE5D,SAAO,MAAM;AACP,QAAA,CAAC0B,SAAS1B,MAAc,QAAA;AAE5B,WAAA3C,YAAA,OAAA;MAAA,SAAA;MAAA,kBAAA;MAAA,cAIgBqE,SAAS1B,MAAMsS,UAAe,KAAA;MAAO,gBACnCwB;MAAW,oBACPpS,SAAS1B,MAAMuS,oBAAoB;MAAC,OACjDxB;IAAAA,GAAU,CAAA1T,YAAA,OAAA;MAAA,SAAA;MAAA,SAEqB;QAAE,CAACE,gBAAgB,IAAIiF,MAAMuR,oBAAoB,MAAM;MAAA;IAAK,GAAC,CAAA1W,YAAA,OAAA;MAAA,MAGzF0N,UAAU/K,QAAQ,YAAYgM,GAAGhM,KAAK,KAAKoB;MAAS,SACjD,uBAAuBoB,MAAMwR,QAAQ,IAAIxR,MAAMwR,KAAK,KAAK;MAAG,SAC5DxR,MAAM1B;IAAAA,GAAK,CAEjB,CAAC0B,MAAMuG,gBAAgBvG,MAAMuG,eAAelM,aAAaqM,QAAK7L,YAAA4Q,eAAA;MAAA,OACzCpR,aAAaqM;MAAK,YAAYxH,SAAS1B;IAAAA,GAAK3C,IAAAA,IAAAA,YAAAyS,iBAAA;MAAA,OAE1CjT,aAAaoX;MAAO,YAAYvS,SAAS1B;IAAAA,GAChE,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAKX;AACF,GACA;EACEgF,MAAM;EACNxC,OAAO,CACL,QACA,SACA,YACA,qBACA,oBACA,qBACA,gBACA,gBACA,iBACA,cACA,uBACA,sBACA,OAAO;;EAGT6C,OAAO6O;AACT,CACF;AAEO,IAAMC,UAAUL;", "names": ["instance", "_TextMeasure_getInstance", "NewLineSymbol", "DiffModeEnum", "useIsMounted", "DiffSplitAddWidget", "side", "className", "lineNumber", "onWidgetClick", "onOpenAddWidget", "_createVNode", "width", "diffFontSizeName", "height", "color", "addWidgetColorName", "backgroundColor", "addWidgetBGName", "onClick", "_createTextVNode", "DiffUnifiedAddWidget", "DiffNoNewLine", "DiffString", "rawLine", "diffLine", "operator", "plainLine", "enableWrap", "enableTemplate", "changes", "hasLineChange", "isNewLineSymbolChanged", "newLineSymbol", "plainTemplate", "getPlainDiffTemplate", "NEWLINE", "range", "str1", "slice", "location", "str2", "length", "str3", "isLast", "includes", "_str2", "replace", "addContentHighlightBGName", "delContentHighlightBGName", "_Fragment", "getSymbol", "template", "getPlainLineTemplate", "value", "DiffSyntax", "syntaxLine", "syntaxTemplate", "getSyntaxDiffTemplate", "nodeList", "map", "node", "wrapper", "index", "endIndex", "startIndex", "properties", "join", "style", "index1", "index2", "isStart", "isEnd", "borderTopLeftRadius", "undefined", "borderBottomLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "getSyntaxLineTemplate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diffFile", "enableHighlight", "isAdded", "type", "DiffLineType", "Add", "isDelete", "Delete", "isMaxLineLengthToIgnoreSyntax", "isEnableTemplate", "getIsEnableTemplate", "whiteSpace", "wordBreak", "DiffSplitContentLine", "props", "setWidget", "useSetWidget", "enableAddWidget", "useEnableAddWidget", "useEnableHighlight", "onAddWidgetClick", "useOnAddWidgetClick", "currentLine", "computed", "SplitSide", "old", "getSplitLeftLine", "getSplitRightLine", "currentLineHasDiff", "diff", "currentLineHasChange", "checkDiffLineIncludeChange", "currentLineHasHidden", "isHidden", "currentLineHasContent", "currentSyntaxLine", "ref", "getOldSyntaxLine", "getNewSyntaxLine", "currentPlainLine", "getOldPlainLine", "getNewPlainLine", "useSubscribeDiffFile", "contentBG", "getContentBG", "lineNumberBG", "getLineNumberBG", "plainLineNumberColorName", "expandLineNumberColorName", "diffAsideWidthName", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "opacity", "emptyBGName", "name", "cb", "DiffSplitExtendLine", "extendData", "useExtendData", "slots", "useSlots", "lineSelector", "lineWrapperSelector", "wrapperSelector", "oldLine", "newLine", "enableExpand", "getExpandEnabled", "oldLineExtend", "oldFile", "newLineExtend", "newFile", "currentItem", "currentIsHidden", "currentExtend", "currentLineNumber", "currentIsShow", "Boolean", "extend", "currentEnable", "extendSide", "new", "useSyncHeight", "selector", "enable", "useDomWidth", "data", "onUpdate", "notifyAll", "ExpandDown", "ExpandUp", "ExpandAll", "DiffSplitHunkLineGitHub", "currentHunk", "getSplitHunkLine", "couldExpand", "splitInfo", "currentShowExpand", "currentSyncHeightSide", "currentIsFirstLine", "<PERSON><PERSON><PERSON><PERSON>", "currentIsLastLine", "currentIsPureHunk", "_getIsPureDiffRender", "currentShowExpandAll", "endHiddenIndex", "startHiddenIndex", "composeLen", "currentEnableSyncHeight", "hunkContentBGName", "hunkLineNumberBGName", "onSplitHunkExpand", "hunkContentColorName", "plainText", "text", "DiffSplitHunkLineGitLab", "DiffSplitHunkLine", "diffViewMode", "useMode", "SplitGitHub", "Split", "DiffSplitWidgetLine", "widget", "useWidget", "oldLineWidget", "newLineWidget", "currentWidget", "observeSide", "onCloseWidget", "onClose", "DiffSplitViewTable", "lines", "getSplitContentLines", "selectState", "onMouseDown", "e", "ele", "target", "nodeName", "removeAllSelection", "HTMLElement", "state", "getAttribute", "current", "onSelect", "parentElement", "item", "splitLineLength", "DiffSplitViewNormal", "isMounted", "ref1", "ref2", "styleRef", "maxText", "Math", "max", "fileLineLength", "toString", "initSyncScroll", "onClean", "left", "right", "clean", "syncScroll", "watchPostEffect", "textContent", "id", "getId", "fontSize", "useFontSize", "font", "fontFamily", "useTextWidth", "computedWidth", "round", "overscrollBehaviorX", "borderColorName", "oldSyntaxLine", "newSyntaxLine", "oldPlainLine", "newPlainLine", "hasDiff", "hasChange", "hasHidden", "hasOldLine", "hasNewLine", "oldLineIsDelete", "newLineIsAdded", "oldLineContentBG", "oldLineNumberBG", "newLineContentBG", "newLineNumberBG", "borderLeftColor", "borderLeftStyle", "oldExtendRendered", "newExtendRendered", "oldWidgetRendered", "newWidgetRendered", "DiffSplitViewWrap", "DiffSplitView", "useEnableWrap", "DiffUnifiedOldLine", "delLineNumberBGName", "delContentBGName", "DiffUnifiedNewLine", "addLineNumberBGName", "addContentBGName", "DiffUnifiedContentLine", "unifiedItem", "getUnifiedLine", "currentItemHasHidden", "currentItemHasChange", "newLineNumber", "oldLineNumber", "plainLineNumberBGName", "expandContentBGName", "plainContentBGName", "DiffUnifiedExtendLine", "oldExtend", "newExtend", "DiffUnifiedHunkLine", "getUnifiedHunkLine", "unifiedInfo", "currentIsEnableAll", "onUnifiedHunkExpand", "DiffUnifiedWidgetLine", "oldWidget", "newWidget", "DiffUnifiedView", "getUnifiedContentLine", "unifiedLineLength", "_cacheMap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "options", "getInstance", "DiffFile", "createInstance", "_mergeFullBundle", "_getFullBundle", "fileName", "content", "hunks", "fileLang", "widgetState", "wrapperRef", "v", "diffViewHighlight", "theme", "diffViewTheme", "clear", "immediate", "initialWidgetState", "deep", "watch", "initSubscribe", "_addClonedInstance", "_delClonedInstance", "initDiff", "initTheme", "initRaw", "buildSplitDiffLines", "buildUnifiedDiffLines", "initSyntax", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initAttribute", "init", "setAttribute", "_getTheme", "_getH<PERSON><PERSON>er<PERSON>ame", "subscribe", "initId", "clearId", "provide", "idSymbol", "mountedSymbol", "slotsSymbol", "onAddWidgetClickSymbol", "emit", "widgetStateSymbol", "setWidgetStateSymbol", "modeSymbol", "defaultValue", "fontSizeSymbol", "enableWrapSymbol", "enableHighlightSymbol", "enableAddWidgetSymbol", "extendDataSymbol", "deepWatch", "onUnmounted", "expose", "getDiffFileInstance", "__VERSION__", "diffViewFontSize", "class", "Unified", "Object", "version"]}