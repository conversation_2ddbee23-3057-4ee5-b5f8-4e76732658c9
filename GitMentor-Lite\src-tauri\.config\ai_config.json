{"base": {"language": "Simplified Chinese", "provider": "Ollama", "model": "qwen2.5:32b"}, "providers": {"openai": {"api_key": "", "base_url": "https://api.openai.com/v1"}, "ollama": {"base_url": "http://*************:11434"}, "zhipu": {"api_key": ""}, "anthropic": {"api_key": ""}, "dashscope": {"api_key": ""}, "doubao": {"api_key": ""}, "gemini": {"api_key": ""}, "deepseek": {"api_key": ""}, "siliconflow": {"api_key": ""}, "openrouter": {"api_key": ""}, "together": {"api_key": ""}, "mistral": {"api_key": ""}, "baidu_qianfan": {"api_key": "", "secret_key": ""}, "azure_openai": {"api_key": "", "endpoint": "", "api_version": "2024-02-01"}, "cloudflare": {"api_key": "", "account_id": ""}, "vertexai": {"project_id": "", "location": "us-central1", "credentials_path": ""}, "groq": {"api_key": ""}}, "features": {"enable_emoji": true, "enable_body": true, "enable_layered_commit": false, "use_recent_commits": true, "enable_streaming": true}, "advanced": {"temperature": 0.7, "max_tokens": 2048, "timeout": 60, "retry_count": 3}}