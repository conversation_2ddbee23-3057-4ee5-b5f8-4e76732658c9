{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 7686550259440415501, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[3834743577069889284, "tauri_plugin_dialog", false, 11619845648598631193], [3935545708480822364, "tauri_plugin_opener", false, 5625995628974734702], [7244058819997729774, "reqwest", false, 14646044918867554991], [8256202458064874477, "dirs", false, 7656500477773596234], [9538054652646069845, "tokio", false, 18289164731111697765], [9689903380558560274, "serde", false, 17765400214494180465], [10755362358622467486, "tauri", false, 15658066461076705409], [11070369641163072442, "handlebars", false, 17709409222617952208], [11946729385090170470, "async_trait", false, 5627391280701608912], [13279321750712491223, "build_script_build", false, 1313391873077341292], [13625485746686963219, "anyhow", false, 1252199605203212593], [15367738274754116744, "serde_json", false, 14830405357018449310], [15609422047640926750, "toml", false, 5435939788833510513], [17240636971104806819, "git2", false, 16233547082066204598]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\gitmentor-lite-c0013276170ec439\\dep-lib-gitmentor_lite_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}