{"version": 3, "sources": ["../../diff/lib/index.mjs", "../../@git-diff-view/file/src/index.ts"], "sourcesContent": ["function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    var self = this;\n    function done(value) {\n      value = self.postProcess(value, options);\n      if (callback) {\n        setTimeout(function () {\n          callback(value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    }\n\n    // Allow subclasses to massage the input prior to running\n    oldString = this.castInput(oldString, options);\n    newString = this.castInput(newString, options);\n    oldString = this.removeEmpty(this.tokenize(oldString, options));\n    newString = this.removeEmpty(this.tokenize(newString, options));\n    var newLen = newString.length,\n      oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n    if (options.maxEditLength != null) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }];\n\n    // Seed editLength = 0, i.e. the content starts with the same values\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0, options);\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done(buildValues(self, bestPath[0].lastComponent, newString, oldString, self.useLongestToken));\n    }\n\n    // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n    var minDiagonalToConsider = -Infinity,\n      maxDiagonalToConsider = Infinity;\n\n    // Main worker method. checks all permutations of a given edit length for acceptance.\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n          addPath = bestPath[diagonalPath + 1];\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n        var canAdd = false;\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        }\n\n        // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        if (!canRemove || canAdd && removePath.oldPos < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, false, 0, options);\n        } else {\n          basePath = self.addToPath(removePath, false, true, 1, options);\n        }\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath, options);\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n      editLength++;\n    }\n\n    // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc, options) {\n    var last = path.lastComponent;\n    if (last && !options.oneChangePerToken && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath, options) {\n    var newLen = newString.length,\n      oldLen = oldString.length,\n      oldPos = basePath.oldPos,\n      newPos = oldPos - diagonalPath,\n      commonCount = 0;\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(oldString[oldPos + 1], newString[newPos + 1], options)) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n      if (options.oneChangePerToken) {\n        basePath.lastComponent = {\n          count: 1,\n          previousComponent: basePath.lastComponent,\n          added: false,\n          removed: false\n        };\n      }\n    }\n    if (commonCount && !options.oneChangePerToken) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent,\n        added: false,\n        removed: false\n      };\n    }\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right, options) {\n    if (options.comparator) {\n      return options.comparator(left, right);\n    } else {\n      return left === right || options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return Array.from(value);\n  },\n  join: function join(chars) {\n    return chars.join('');\n  },\n  postProcess: function postProcess(changeObjects) {\n    return changeObjects;\n  }\n};\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n  components.reverse();\n  var componentPos = 0,\n    componentLen = components.length,\n    newPos = 0,\n    oldPos = 0;\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n      newPos += component.count;\n\n      // Common case\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count;\n    }\n  }\n  return components;\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction longestCommonPrefix(str1, str2) {\n  var i;\n  for (i = 0; i < str1.length && i < str2.length; i++) {\n    if (str1[i] != str2[i]) {\n      return str1.slice(0, i);\n    }\n  }\n  return str1.slice(0, i);\n}\nfunction longestCommonSuffix(str1, str2) {\n  var i;\n\n  // Unlike longestCommonPrefix, we need a special case to handle all scenarios\n  // where we return the empty string since str1.slice(-0) will return the\n  // entire string.\n  if (!str1 || !str2 || str1[str1.length - 1] != str2[str2.length - 1]) {\n    return '';\n  }\n  for (i = 0; i < str1.length && i < str2.length; i++) {\n    if (str1[str1.length - (i + 1)] != str2[str2.length - (i + 1)]) {\n      return str1.slice(-i);\n    }\n  }\n  return str1.slice(-i);\n}\nfunction replacePrefix(string, oldPrefix, newPrefix) {\n  if (string.slice(0, oldPrefix.length) != oldPrefix) {\n    throw Error(\"string \".concat(JSON.stringify(string), \" doesn't start with prefix \").concat(JSON.stringify(oldPrefix), \"; this is a bug\"));\n  }\n  return newPrefix + string.slice(oldPrefix.length);\n}\nfunction replaceSuffix(string, oldSuffix, newSuffix) {\n  if (!oldSuffix) {\n    return string + newSuffix;\n  }\n  if (string.slice(-oldSuffix.length) != oldSuffix) {\n    throw Error(\"string \".concat(JSON.stringify(string), \" doesn't end with suffix \").concat(JSON.stringify(oldSuffix), \"; this is a bug\"));\n  }\n  return string.slice(0, -oldSuffix.length) + newSuffix;\n}\nfunction removePrefix(string, oldPrefix) {\n  return replacePrefix(string, oldPrefix, '');\n}\nfunction removeSuffix(string, oldSuffix) {\n  return replaceSuffix(string, oldSuffix, '');\n}\nfunction maximumOverlap(string1, string2) {\n  return string2.slice(0, overlapCount(string1, string2));\n}\n\n// Nicked from https://stackoverflow.com/a/60422853/1709587\nfunction overlapCount(a, b) {\n  // Deal with cases where the strings differ in length\n  var startA = 0;\n  if (a.length > b.length) {\n    startA = a.length - b.length;\n  }\n  var endB = b.length;\n  if (a.length < b.length) {\n    endB = a.length;\n  }\n  // Create a back-reference for each index\n  //   that should be followed in case of a mismatch.\n  //   We only need B to make these references:\n  var map = Array(endB);\n  var k = 0; // Index that lags behind j\n  map[0] = 0;\n  for (var j = 1; j < endB; j++) {\n    if (b[j] == b[k]) {\n      map[j] = map[k]; // skip over the same character (optional optimisation)\n    } else {\n      map[j] = k;\n    }\n    while (k > 0 && b[j] != b[k]) {\n      k = map[k];\n    }\n    if (b[j] == b[k]) {\n      k++;\n    }\n  }\n  // Phase 2: use these references while iterating over A\n  k = 0;\n  for (var i = startA; i < a.length; i++) {\n    while (k > 0 && a[i] != b[k]) {\n      k = map[k];\n    }\n    if (a[i] == b[k]) {\n      k++;\n    }\n  }\n  return k;\n}\n\n/**\n * Returns true if the string consistently uses Windows line endings.\n */\nfunction hasOnlyWinLineEndings(string) {\n  return string.includes('\\r\\n') && !string.startsWith('\\n') && !string.match(/[^\\r]\\n/);\n}\n\n/**\n * Returns true if the string consistently uses Unix line endings.\n */\nfunction hasOnlyUnixLineEndings(string) {\n  return !string.includes('\\r\\n') && string.includes('\\n');\n}\n\n// Based on https://en.wikipedia.org/wiki/Latin_script_in_Unicode\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\nvar extendedWordChars = \"a-zA-Z0-9_\\\\u{C0}-\\\\u{FF}\\\\u{D8}-\\\\u{F6}\\\\u{F8}-\\\\u{2C6}\\\\u{2C8}-\\\\u{2D7}\\\\u{2DE}-\\\\u{2FF}\\\\u{1E00}-\\\\u{1EFF}\";\n\n// Each token is one of the following:\n// - A punctuation mark plus the surrounding whitespace\n// - A word plus the surrounding whitespace\n// - Pure whitespace (but only in the special case where this the entire text\n//   is just whitespace)\n//\n// We have to include surrounding whitespace in the tokens because the two\n// alternative approaches produce horribly broken results:\n// * If we just discard the whitespace, we can't fully reproduce the original\n//   text from the sequence of tokens and any attempt to render the diff will\n//   get the whitespace wrong.\n// * If we have separate tokens for whitespace, then in a typical text every\n//   second token will be a single space character. But this often results in\n//   the optimal diff between two texts being a perverse one that preserves\n//   the spaces between words but deletes and reinserts actual common words.\n//   See https://github.com/kpdecker/jsdiff/issues/160#issuecomment-1866099640\n//   for an example.\n//\n// Keeping the surrounding whitespace of course has implications for .equals\n// and .join, not just .tokenize.\n\n// This regex does NOT fully implement the tokenization rules described above.\n// Instead, it gives runs of whitespace their own \"token\". The tokenize method\n// then handles stitching whitespace tokens onto adjacent word or punctuation\n// tokens.\nvar tokenizeIncludingWhitespace = new RegExp(\"[\".concat(extendedWordChars, \"]+|\\\\s+|[^\").concat(extendedWordChars, \"]\"), 'ug');\nvar wordDiff = new Diff();\nwordDiff.equals = function (left, right, options) {\n  if (options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n  return left.trim() === right.trim();\n};\nwordDiff.tokenize = function (value) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var parts;\n  if (options.intlSegmenter) {\n    if (options.intlSegmenter.resolvedOptions().granularity != 'word') {\n      throw new Error('The segmenter passed must have a granularity of \"word\"');\n    }\n    parts = Array.from(options.intlSegmenter.segment(value), function (segment) {\n      return segment.segment;\n    });\n  } else {\n    parts = value.match(tokenizeIncludingWhitespace) || [];\n  }\n  var tokens = [];\n  var prevPart = null;\n  parts.forEach(function (part) {\n    if (/\\s/.test(part)) {\n      if (prevPart == null) {\n        tokens.push(part);\n      } else {\n        tokens.push(tokens.pop() + part);\n      }\n    } else if (/\\s/.test(prevPart)) {\n      if (tokens[tokens.length - 1] == prevPart) {\n        tokens.push(tokens.pop() + part);\n      } else {\n        tokens.push(prevPart + part);\n      }\n    } else {\n      tokens.push(part);\n    }\n    prevPart = part;\n  });\n  return tokens;\n};\nwordDiff.join = function (tokens) {\n  // Tokens being joined here will always have appeared consecutively in the\n  // same text, so we can simply strip off the leading whitespace from all the\n  // tokens except the first (and except any whitespace-only tokens - but such\n  // a token will always be the first and only token anyway) and then join them\n  // and the whitespace around words and punctuation will end up correct.\n  return tokens.map(function (token, i) {\n    if (i == 0) {\n      return token;\n    } else {\n      return token.replace(/^\\s+/, '');\n    }\n  }).join('');\n};\nwordDiff.postProcess = function (changes, options) {\n  if (!changes || options.oneChangePerToken) {\n    return changes;\n  }\n  var lastKeep = null;\n  // Change objects representing any insertion or deletion since the last\n  // \"keep\" change object. There can be at most one of each.\n  var insertion = null;\n  var deletion = null;\n  changes.forEach(function (change) {\n    if (change.added) {\n      insertion = change;\n    } else if (change.removed) {\n      deletion = change;\n    } else {\n      if (insertion || deletion) {\n        // May be false at start of text\n        dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, change);\n      }\n      lastKeep = change;\n      insertion = null;\n      deletion = null;\n    }\n  });\n  if (insertion || deletion) {\n    dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, null);\n  }\n  return changes;\n};\nfunction diffWords(oldStr, newStr, options) {\n  // This option has never been documented and never will be (it's clearer to\n  // just call `diffWordsWithSpace` directly if you need that behavior), but\n  // has existed in jsdiff for a long time, so we retain support for it here\n  // for the sake of backwards compatibility.\n  if ((options === null || options === void 0 ? void 0 : options.ignoreWhitespace) != null && !options.ignoreWhitespace) {\n    return diffWordsWithSpace(oldStr, newStr, options);\n  }\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction dedupeWhitespaceInChangeObjects(startKeep, deletion, insertion, endKeep) {\n  // Before returning, we tidy up the leading and trailing whitespace of the\n  // change objects to eliminate cases where trailing whitespace in one object\n  // is repeated as leading whitespace in the next.\n  // Below are examples of the outcomes we want here to explain the code.\n  // I=insert, K=keep, D=delete\n  // 1. diffing 'foo bar baz' vs 'foo baz'\n  //    Prior to cleanup, we have K:'foo ' D:' bar ' K:' baz'\n  //    After cleanup, we want:   K:'foo ' D:'bar ' K:'baz'\n  //\n  // 2. Diffing 'foo bar baz' vs 'foo qux baz'\n  //    Prior to cleanup, we have K:'foo ' D:' bar ' I:' qux ' K:' baz'\n  //    After cleanup, we want K:'foo ' D:'bar' I:'qux' K:' baz'\n  //\n  // 3. Diffing 'foo\\nbar baz' vs 'foo baz'\n  //    Prior to cleanup, we have K:'foo ' D:'\\nbar ' K:' baz'\n  //    After cleanup, we want K'foo' D:'\\nbar' K:' baz'\n  //\n  // 4. Diffing 'foo baz' vs 'foo\\nbar baz'\n  //    Prior to cleanup, we have K:'foo\\n' I:'\\nbar ' K:' baz'\n  //    After cleanup, we ideally want K'foo' I:'\\nbar' K:' baz'\n  //    but don't actually manage this currently (the pre-cleanup change\n  //    objects don't contain enough information to make it possible).\n  //\n  // 5. Diffing 'foo   bar baz' vs 'foo  baz'\n  //    Prior to cleanup, we have K:'foo  ' D:'   bar ' K:'  baz'\n  //    After cleanup, we want K:'foo  ' D:' bar ' K:'baz'\n  //\n  // Our handling is unavoidably imperfect in the case where there's a single\n  // indel between keeps and the whitespace has changed. For instance, consider\n  // diffing 'foo\\tbar\\nbaz' vs 'foo baz'. Unless we create an extra change\n  // object to represent the insertion of the space character (which isn't even\n  // a token), we have no way to avoid losing information about the texts'\n  // original whitespace in the result we return. Still, we do our best to\n  // output something that will look sensible if we e.g. print it with\n  // insertions in green and deletions in red.\n\n  // Between two \"keep\" change objects (or before the first or after the last\n  // change object), we can have either:\n  // * A \"delete\" followed by an \"insert\"\n  // * Just an \"insert\"\n  // * Just a \"delete\"\n  // We handle the three cases separately.\n  if (deletion && insertion) {\n    var oldWsPrefix = deletion.value.match(/^\\s*/)[0];\n    var oldWsSuffix = deletion.value.match(/\\s*$/)[0];\n    var newWsPrefix = insertion.value.match(/^\\s*/)[0];\n    var newWsSuffix = insertion.value.match(/\\s*$/)[0];\n    if (startKeep) {\n      var commonWsPrefix = longestCommonPrefix(oldWsPrefix, newWsPrefix);\n      startKeep.value = replaceSuffix(startKeep.value, newWsPrefix, commonWsPrefix);\n      deletion.value = removePrefix(deletion.value, commonWsPrefix);\n      insertion.value = removePrefix(insertion.value, commonWsPrefix);\n    }\n    if (endKeep) {\n      var commonWsSuffix = longestCommonSuffix(oldWsSuffix, newWsSuffix);\n      endKeep.value = replacePrefix(endKeep.value, newWsSuffix, commonWsSuffix);\n      deletion.value = removeSuffix(deletion.value, commonWsSuffix);\n      insertion.value = removeSuffix(insertion.value, commonWsSuffix);\n    }\n  } else if (insertion) {\n    // The whitespaces all reflect what was in the new text rather than\n    // the old, so we essentially have no information about whitespace\n    // insertion or deletion. We just want to dedupe the whitespace.\n    // We do that by having each change object keep its trailing\n    // whitespace and deleting duplicate leading whitespace where\n    // present.\n    if (startKeep) {\n      insertion.value = insertion.value.replace(/^\\s*/, '');\n    }\n    if (endKeep) {\n      endKeep.value = endKeep.value.replace(/^\\s*/, '');\n    }\n    // otherwise we've got a deletion and no insertion\n  } else if (startKeep && endKeep) {\n    var newWsFull = endKeep.value.match(/^\\s*/)[0],\n      delWsStart = deletion.value.match(/^\\s*/)[0],\n      delWsEnd = deletion.value.match(/\\s*$/)[0];\n\n    // Any whitespace that comes straight after startKeep in both the old and\n    // new texts, assign to startKeep and remove from the deletion.\n    var newWsStart = longestCommonPrefix(newWsFull, delWsStart);\n    deletion.value = removePrefix(deletion.value, newWsStart);\n\n    // Any whitespace that comes straight before endKeep in both the old and\n    // new texts, and hasn't already been assigned to startKeep, assign to\n    // endKeep and remove from the deletion.\n    var newWsEnd = longestCommonSuffix(removePrefix(newWsFull, newWsStart), delWsEnd);\n    deletion.value = removeSuffix(deletion.value, newWsEnd);\n    endKeep.value = replacePrefix(endKeep.value, newWsFull, newWsEnd);\n\n    // If there's any whitespace from the new text that HASN'T already been\n    // assigned, assign it to the start:\n    startKeep.value = replaceSuffix(startKeep.value, newWsFull, newWsFull.slice(0, newWsFull.length - newWsEnd.length));\n  } else if (endKeep) {\n    // We are at the start of the text. Preserve all the whitespace on\n    // endKeep, and just remove whitespace from the end of deletion to the\n    // extent that it overlaps with the start of endKeep.\n    var endKeepWsPrefix = endKeep.value.match(/^\\s*/)[0];\n    var deletionWsSuffix = deletion.value.match(/\\s*$/)[0];\n    var overlap = maximumOverlap(deletionWsSuffix, endKeepWsPrefix);\n    deletion.value = removeSuffix(deletion.value, overlap);\n  } else if (startKeep) {\n    // We are at the END of the text. Preserve all the whitespace on\n    // startKeep, and just remove whitespace from the start of deletion to\n    // the extent that it overlaps with the end of startKeep.\n    var startKeepWsSuffix = startKeep.value.match(/\\s*$/)[0];\n    var deletionWsPrefix = deletion.value.match(/^\\s*/)[0];\n    var _overlap = maximumOverlap(startKeepWsSuffix, deletionWsPrefix);\n    deletion.value = removePrefix(deletion.value, _overlap);\n  }\n}\nvar wordWithSpaceDiff = new Diff();\nwordWithSpaceDiff.tokenize = function (value) {\n  // Slightly different to the tokenizeIncludingWhitespace regex used above in\n  // that this one treats each individual newline as a distinct tokens, rather\n  // than merging them into other surrounding whitespace. This was requested\n  // in https://github.com/kpdecker/jsdiff/issues/180 &\n  //    https://github.com/kpdecker/jsdiff/issues/211\n  var regex = new RegExp(\"(\\\\r?\\\\n)|[\".concat(extendedWordChars, \"]+|[^\\\\S\\\\n\\\\r]+|[^\").concat(extendedWordChars, \"]\"), 'ug');\n  return value.match(regex) || [];\n};\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordWithSpaceDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n  return defaults;\n}\n\nvar lineDiff = new Diff();\nlineDiff.tokenize = function (value, options) {\n  if (options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n  var retLines = [],\n    linesAndNewlines = value.split(/(\\n|\\r\\n)/);\n\n  // Ignore the final empty token that occurs if the string ends with a new line\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  }\n\n  // Merge the content and line separators into single tokens\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n    if (i % 2 && !options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      retLines.push(line);\n    }\n  }\n  return retLines;\n};\nlineDiff.equals = function (left, right, options) {\n  // If we're ignoring whitespace, we need to normalise lines by stripping\n  // whitespace before checking equality. (This has an annoying interaction\n  // with newlineIsToken that requires special handling: if newlines get their\n  // own token, then we DON'T want to trim the *newline* tokens down to empty\n  // strings, since this would cause us to treat whitespace-only line content\n  // as equal to a separator between lines, which would be weird and\n  // inconsistent with the documented behavior of the options.)\n  if (options.ignoreWhitespace) {\n    if (!options.newlineIsToken || !left.includes('\\n')) {\n      left = left.trim();\n    }\n    if (!options.newlineIsToken || !right.includes('\\n')) {\n      right = right.trim();\n    }\n  } else if (options.ignoreNewlineAtEof && !options.newlineIsToken) {\n    if (left.endsWith('\\n')) {\n      left = left.slice(0, -1);\n    }\n    if (right.endsWith('\\n')) {\n      right = right.slice(0, -1);\n    }\n  }\n  return Diff.prototype.equals.call(this, left, right, options);\n};\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\n\n// Kept for backwards compatibility. This is a rather arbitrary wrapper method\n// that just calls `diffLines` with `ignoreWhitespace: true`. It's confusing to\n// have two ways to do exactly the same thing in the API, so we no longer\n// document this one (library users should explicitly use `diffLines` with\n// `ignoreWhitespace: true` instead) but we keep it around to maintain\n// compatibility with code that used old versions.\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar jsonDiff = new Diff();\n// Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\njsonDiff.castInput = function (value, options) {\n  var undefinedReplacement = options.undefinedReplacement,\n    _options$stringifyRep = options.stringifyReplacer,\n    stringifyReplacer = _options$stringifyRep === void 0 ? function (k, v) {\n      return typeof v === 'undefined' ? undefinedReplacement : v;\n    } : _options$stringifyRep;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\njsonDiff.equals = function (left, right, options) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'), options);\n};\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n}\n\n// This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n  var i;\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n  var canonicalizedObj;\n  if ('[object Array]' === Object.prototype.toString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n    var sortedKeys = [],\n      _key;\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (Object.prototype.hasOwnProperty.call(obj, _key)) {\n        sortedKeys.push(_key);\n      }\n    }\n    sortedKeys.sort();\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction unixToWin(patch) {\n  if (Array.isArray(patch)) {\n    return patch.map(unixToWin);\n  }\n  return _objectSpread2(_objectSpread2({}, patch), {}, {\n    hunks: patch.hunks.map(function (hunk) {\n      return _objectSpread2(_objectSpread2({}, hunk), {}, {\n        lines: hunk.lines.map(function (line, i) {\n          var _hunk$lines;\n          return line.startsWith('\\\\') || line.endsWith('\\r') || (_hunk$lines = hunk.lines[i + 1]) !== null && _hunk$lines !== void 0 && _hunk$lines.startsWith('\\\\') ? line : line + '\\r';\n        })\n      });\n    })\n  });\n}\nfunction winToUnix(patch) {\n  if (Array.isArray(patch)) {\n    return patch.map(winToUnix);\n  }\n  return _objectSpread2(_objectSpread2({}, patch), {}, {\n    hunks: patch.hunks.map(function (hunk) {\n      return _objectSpread2(_objectSpread2({}, hunk), {}, {\n        lines: hunk.lines.map(function (line) {\n          return line.endsWith('\\r') ? line.substring(0, line.length - 1) : line;\n        })\n      });\n    })\n  });\n}\n\n/**\n * Returns true if the patch consistently uses Unix line endings (or only involves one line and has\n * no line endings).\n */\nfunction isUnix(patch) {\n  if (!Array.isArray(patch)) {\n    patch = [patch];\n  }\n  return !patch.some(function (index) {\n    return index.hunks.some(function (hunk) {\n      return hunk.lines.some(function (line) {\n        return !line.startsWith('\\\\') && line.endsWith('\\r');\n      });\n    });\n  });\n}\n\n/**\n * Returns true if the patch uses Windows line endings and only Windows line endings.\n */\nfunction isWin(patch) {\n  if (!Array.isArray(patch)) {\n    patch = [patch];\n  }\n  return patch.some(function (index) {\n    return index.hunks.some(function (hunk) {\n      return hunk.lines.some(function (line) {\n        return line.endsWith('\\r');\n      });\n    });\n  }) && patch.every(function (index) {\n    return index.hunks.every(function (hunk) {\n      return hunk.lines.every(function (line, i) {\n        var _hunk$lines2;\n        return line.startsWith('\\\\') || line.endsWith('\\r') || ((_hunk$lines2 = hunk.lines[i + 1]) === null || _hunk$lines2 === void 0 ? void 0 : _hunk$lines2.startsWith('\\\\'));\n      });\n    });\n  });\n}\n\nfunction parsePatch(uniDiff) {\n  var diffstr = uniDiff.split(/\\n/),\n    list = [],\n    i = 0;\n  function parseIndex() {\n    var index = {};\n    list.push(index);\n\n    // Parse diff metadata\n    while (i < diffstr.length) {\n      var line = diffstr[i];\n\n      // File header found, end parsing diff metadata\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      }\n\n      // Diff index\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n      if (header) {\n        index.index = header[1];\n      }\n      i++;\n    }\n\n    // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n    parseFileHeader(index);\n    parseFileHeader(index);\n\n    // Parse hunks\n    index.hunks = [];\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n      if (/^(Index:\\s|diff\\s|\\-\\-\\-\\s|\\+\\+\\+\\s|===================================================================)/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line) {\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  }\n\n  // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)\\r?$/.exec(diffstr[i]);\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  }\n\n  // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n      chunkHeaderLine = diffstr[i++],\n      chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: []\n    };\n\n    // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n    var addCount = 0,\n      removeCount = 0;\n    for (; i < diffstr.length && (removeCount < hunk.oldLines || addCount < hunk.newLines || (_diffstr$i = diffstr[i]) !== null && _diffstr$i !== void 0 && _diffstr$i.startsWith('\\\\')); i++) {\n      var _diffstr$i;\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        throw new Error(\"Hunk at line \".concat(chunkHeaderIndex + 1, \" contained invalid line \").concat(diffstr[i]));\n      }\n    }\n\n    // Handle the empty block count case\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    }\n\n    // Perform sanity checking\n    if (addCount !== hunk.newLines) {\n      throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n    }\n    if (removeCount !== hunk.oldLines) {\n      throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n    }\n    return hunk;\n  }\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n    backwardExhausted = false,\n    forwardExhausted = false,\n    localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      }\n\n      // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n      if (start + localOffset <= maxLine) {\n        return start + localOffset;\n      }\n      forwardExhausted = true;\n    }\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      }\n\n      // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n      if (minLine <= start - localOffset) {\n        return start - localOffset++;\n      }\n      backwardExhausted = true;\n      return iterator();\n    }\n\n    // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n    uniDiff = uniDiff[0];\n  }\n  if (options.autoConvertLineEndings || options.autoConvertLineEndings == null) {\n    if (hasOnlyWinLineEndings(source) && isUnix(uniDiff)) {\n      uniDiff = unixToWin(uniDiff);\n    } else if (hasOnlyUnixLineEndings(source) && isWin(uniDiff)) {\n      uniDiff = winToUnix(uniDiff);\n    }\n  }\n\n  // Apply the diff to the input\n  var lines = source.split('\\n'),\n    hunks = uniDiff.hunks,\n    compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n      return line === patchContent;\n    },\n    fuzzFactor = options.fuzzFactor || 0,\n    minLine = 0;\n  if (fuzzFactor < 0 || !Number.isInteger(fuzzFactor)) {\n    throw new Error('fuzzFactor must be a non-negative integer');\n  }\n\n  // Special case for empty patch.\n  if (!hunks.length) {\n    return source;\n  }\n\n  // Before anything else, handle EOFNL insertion/removal. If the patch tells us to make a change\n  // to the EOFNL that is redundant/impossible - i.e. to remove a newline that's not there, or add a\n  // newline that already exists - then we either return false and fail to apply the patch (if\n  // fuzzFactor is 0) or simply ignore the problem and do nothing (if fuzzFactor is >0).\n  // If we do need to remove/add a newline at EOF, this will always be in the final hunk:\n  var prevLine = '',\n    removeEOFNL = false,\n    addEOFNL = false;\n  for (var i = 0; i < hunks[hunks.length - 1].lines.length; i++) {\n    var line = hunks[hunks.length - 1].lines[i];\n    if (line[0] == '\\\\') {\n      if (prevLine[0] == '+') {\n        removeEOFNL = true;\n      } else if (prevLine[0] == '-') {\n        addEOFNL = true;\n      }\n    }\n    prevLine = line;\n  }\n  if (removeEOFNL) {\n    if (addEOFNL) {\n      // This means the final line gets changed but doesn't have a trailing newline in either the\n      // original or patched version. In that case, we do nothing if fuzzFactor > 0, and if\n      // fuzzFactor is 0, we simply validate that the source file has no trailing newline.\n      if (!fuzzFactor && lines[lines.length - 1] == '') {\n        return false;\n      }\n    } else if (lines[lines.length - 1] == '') {\n      lines.pop();\n    } else if (!fuzzFactor) {\n      return false;\n    }\n  } else if (addEOFNL) {\n    if (lines[lines.length - 1] != '') {\n      lines.push('');\n    } else if (!fuzzFactor) {\n      return false;\n    }\n  }\n\n  /**\n   * Checks if the hunk can be made to fit at the provided location with at most `maxErrors`\n   * insertions, substitutions, or deletions, while ensuring also that:\n   * - lines deleted in the hunk match exactly, and\n   * - wherever an insertion operation or block of insertion operations appears in the hunk, the\n   *   immediately preceding and following lines of context match exactly\n   *\n   * `toPos` should be set such that lines[toPos] is meant to match hunkLines[0].\n   *\n   * If the hunk can be applied, returns an object with properties `oldLineLastI` and\n   * `replacementLines`. Otherwise, returns null.\n   */\n  function applyHunk(hunkLines, toPos, maxErrors) {\n    var hunkLinesI = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    var lastContextLineMatched = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    var patchedLines = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [];\n    var patchedLinesLength = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 0;\n    var nConsecutiveOldContextLines = 0;\n    var nextContextLineMustMatch = false;\n    for (; hunkLinesI < hunkLines.length; hunkLinesI++) {\n      var hunkLine = hunkLines[hunkLinesI],\n        operation = hunkLine.length > 0 ? hunkLine[0] : ' ',\n        content = hunkLine.length > 0 ? hunkLine.substr(1) : hunkLine;\n      if (operation === '-') {\n        if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n          toPos++;\n          nConsecutiveOldContextLines = 0;\n        } else {\n          if (!maxErrors || lines[toPos] == null) {\n            return null;\n          }\n          patchedLines[patchedLinesLength] = lines[toPos];\n          return applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1);\n        }\n      }\n      if (operation === '+') {\n        if (!lastContextLineMatched) {\n          return null;\n        }\n        patchedLines[patchedLinesLength] = content;\n        patchedLinesLength++;\n        nConsecutiveOldContextLines = 0;\n        nextContextLineMustMatch = true;\n      }\n      if (operation === ' ') {\n        nConsecutiveOldContextLines++;\n        patchedLines[patchedLinesLength] = lines[toPos];\n        if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n          patchedLinesLength++;\n          lastContextLineMatched = true;\n          nextContextLineMustMatch = false;\n          toPos++;\n        } else {\n          if (nextContextLineMustMatch || !maxErrors) {\n            return null;\n          }\n\n          // Consider 3 possibilities in sequence:\n          // 1. lines contains a *substitution* not included in the patch context, or\n          // 2. lines contains an *insertion* not included in the patch context, or\n          // 3. lines contains a *deletion* not included in the patch context\n          // The first two options are of course only possible if the line from lines is non-null -\n          // i.e. only option 3 is possible if we've overrun the end of the old file.\n          return lines[toPos] && (applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength + 1) || applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1)) || applyHunk(hunkLines, toPos, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength);\n        }\n      }\n    }\n\n    // Before returning, trim any unmodified context lines off the end of patchedLines and reduce\n    // toPos (and thus oldLineLastI) accordingly. This allows later hunks to be applied to a region\n    // that starts in this hunk's trailing context.\n    patchedLinesLength -= nConsecutiveOldContextLines;\n    toPos -= nConsecutiveOldContextLines;\n    patchedLines.length = patchedLinesLength;\n    return {\n      patchedLines: patchedLines,\n      oldLineLastI: toPos - 1\n    };\n  }\n  var resultLines = [];\n\n  // Search best fit offsets for each hunk based on the previous ones\n  var prevHunkOffset = 0;\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var hunk = hunks[_i];\n    var hunkResult = void 0;\n    var maxLine = lines.length - hunk.oldLines + fuzzFactor;\n    var toPos = void 0;\n    for (var maxErrors = 0; maxErrors <= fuzzFactor; maxErrors++) {\n      toPos = hunk.oldStart + prevHunkOffset - 1;\n      var iterator = distanceIterator(toPos, minLine, maxLine);\n      for (; toPos !== undefined; toPos = iterator()) {\n        hunkResult = applyHunk(hunk.lines, toPos, maxErrors);\n        if (hunkResult) {\n          break;\n        }\n      }\n      if (hunkResult) {\n        break;\n      }\n    }\n    if (!hunkResult) {\n      return false;\n    }\n\n    // Copy everything from the end of where we applied the last hunk to the start of this hunk\n    for (var _i2 = minLine; _i2 < toPos; _i2++) {\n      resultLines.push(lines[_i2]);\n    }\n\n    // Add the lines produced by applying the hunk:\n    for (var _i3 = 0; _i3 < hunkResult.patchedLines.length; _i3++) {\n      var _line = hunkResult.patchedLines[_i3];\n      resultLines.push(_line);\n    }\n\n    // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n    minLine = hunkResult.oldLineLastI + 1;\n\n    // Note the offset between where the patch said the hunk should've applied and where we\n    // applied it, so we can adjust future hunks accordingly:\n    prevHunkOffset = toPos + 1 - hunk.oldStart;\n  }\n\n  // Copy over the rest of the lines from the old text\n  for (var _i4 = minLine; _i4 < lines.length; _i4++) {\n    resultLines.push(lines[_i4]);\n  }\n  return resultLines.join('\\n');\n}\n\n// Wrapper that supports multiple file patches via callbacks.\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  var currentIndex = 0;\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n    if (!index) {\n      return options.complete();\n    }\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n        processIndex();\n      });\n    });\n  }\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n  if (typeof options === 'function') {\n    options = {\n      callback: options\n    };\n  }\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n  if (options.newlineIsToken) {\n    throw new Error('newlineIsToken may not be used with patch-generation functions, only with diffing functions');\n  }\n  if (!options.callback) {\n    return diffLinesResultToPatch(diffLines(oldStr, newStr, options));\n  } else {\n    var _options = options,\n      _callback = _options.callback;\n    diffLines(oldStr, newStr, _objectSpread2(_objectSpread2({}, options), {}, {\n      callback: function callback(diff) {\n        var patch = diffLinesResultToPatch(diff);\n        _callback(patch);\n      }\n    }));\n  }\n  function diffLinesResultToPatch(diff) {\n    // STEP 1: Build up the patch with no \"\\ No newline at end of file\" lines and with the arrays\n    //         of lines containing trailing newline characters. We'll tidy up later...\n\n    if (!diff) {\n      return;\n    }\n    diff.push({\n      value: '',\n      lines: []\n    }); // Append an empty value to make cleanup easier\n\n    function contextLines(lines) {\n      return lines.map(function (entry) {\n        return ' ' + entry;\n      });\n    }\n    var hunks = [];\n    var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n    var _loop = function _loop() {\n      var current = diff[i],\n        lines = current.lines || splitLines(current.value);\n      current.lines = lines;\n      if (current.added || current.removed) {\n        var _curRange;\n        // If we have previous context, start with that\n        if (!oldRangeStart) {\n          var prev = diff[i - 1];\n          oldRangeStart = oldLine;\n          newRangeStart = newLine;\n          if (prev) {\n            curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n            oldRangeStart -= curRange.length;\n            newRangeStart -= curRange.length;\n          }\n        }\n\n        // Output our changes\n        (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n          return (current.added ? '+' : '-') + entry;\n        })));\n\n        // Track the updated file position\n        if (current.added) {\n          newLine += lines.length;\n        } else {\n          oldLine += lines.length;\n        }\n      } else {\n        // Identical context lines. Track line changes\n        if (oldRangeStart) {\n          // Close out any changes that have been output (or join overlapping)\n          if (lines.length <= options.context * 2 && i < diff.length - 2) {\n            var _curRange2;\n            // Overlapping\n            (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n          } else {\n            var _curRange3;\n            // end the range and output\n            var contextSize = Math.min(lines.length, options.context);\n            (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n            var _hunk = {\n              oldStart: oldRangeStart,\n              oldLines: oldLine - oldRangeStart + contextSize,\n              newStart: newRangeStart,\n              newLines: newLine - newRangeStart + contextSize,\n              lines: curRange\n            };\n            hunks.push(_hunk);\n            oldRangeStart = 0;\n            newRangeStart = 0;\n            curRange = [];\n          }\n        }\n        oldLine += lines.length;\n        newLine += lines.length;\n      }\n    };\n    for (var i = 0; i < diff.length; i++) {\n      _loop();\n    }\n\n    // Step 2: eliminate the trailing `\\n` from each line of each hunk, and, where needed, add\n    //         \"\\ No newline at end of file\".\n    for (var _i = 0, _hunks = hunks; _i < _hunks.length; _i++) {\n      var hunk = _hunks[_i];\n      for (var _i2 = 0; _i2 < hunk.lines.length; _i2++) {\n        if (hunk.lines[_i2].endsWith('\\n')) {\n          hunk.lines[_i2] = hunk.lines[_i2].slice(0, -1);\n        } else {\n          hunk.lines.splice(_i2 + 1, 0, '\\\\ No newline at end of file');\n          _i2++; // Skip the line we just added, then continue iterating\n        }\n      }\n    }\n    return {\n      oldFileName: oldFileName,\n      newFileName: newFileName,\n      oldHeader: oldHeader,\n      newHeader: newHeader,\n      hunks: hunks\n    };\n  }\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n  var ret = [];\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i];\n    // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  var _options2;\n  if (typeof options === 'function') {\n    options = {\n      callback: options\n    };\n  }\n  if (!((_options2 = options) !== null && _options2 !== void 0 && _options2.callback)) {\n    var patchObj = structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options);\n    if (!patchObj) {\n      return;\n    }\n    return formatPatch(patchObj);\n  } else {\n    var _options3 = options,\n      _callback2 = _options3.callback;\n    structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, _objectSpread2(_objectSpread2({}, options), {}, {\n      callback: function callback(patchObj) {\n        if (!patchObj) {\n          _callback2();\n        } else {\n          _callback2(formatPatch(patchObj));\n        }\n      }\n    }));\n  }\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\n/**\n * Split `text` into an array of lines, including the trailing newline character (where present)\n */\nfunction splitLines(text) {\n  var hasTrailingNl = text.endsWith('\\n');\n  var result = text.split('\\n').map(function (line) {\n    return line + '\\n';\n  });\n  if (hasTrailingNl) {\n    result.pop();\n  } else {\n    result.push(result.pop().slice(0, -1));\n  }\n  return result;\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n    oldLines = _calcOldNewLineCount.oldLines,\n    newLines = _calcOldNewLineCount.newLines;\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {};\n\n  // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n  ret.hunks = [];\n  var mineIndex = 0,\n    theirsIndex = 0,\n    mineOffset = 0,\n    theirsOffset = 0;\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n        oldStart: Infinity\n      },\n      theirsCurrent = theirs.hunks[theirsIndex] || {\n        oldStart: Infinity\n      };\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n  return ret;\n}\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n    return structuredPatch(undefined, undefined, base, param);\n  }\n  return param;\n}\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n      offset: mineOffset,\n      lines: mineLines,\n      index: 0\n    },\n    their = {\n      offset: theirOffset,\n      lines: theirLines,\n      index: 0\n    };\n\n  // Handle any leading content\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine);\n\n  // Now in the overlap content. Scan through and select the best changes from each.\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n      theirCurrent = their.lines[their.index];\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  }\n\n  // Now push anything that may be remaining\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectChange(their);\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n    return;\n  }\n  conflict(hunk, myChanges, theirChanges);\n}\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectContext(their, myChanges);\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\nfunction collectChange(state) {\n  var ret = [],\n    operation = state.lines[state.index][0];\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index];\n\n    // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n  return ret;\n}\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n    merged = [],\n    matchIndex = 0,\n    contextChanges = false,\n    conflicted = false;\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n      match = matchChanges[matchIndex];\n\n    // Once we've hit our add, then we are done\n    if (match[0] === '+') {\n      break;\n    }\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++;\n\n    // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n    if (change[0] === '+') {\n      conflicted = true;\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n  if (conflicted) {\n    return changes;\n  }\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n  state.index += delta;\n  return true;\n}\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n    change,\n    operation;\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n    ret.push([operation, change.value]);\n  }\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n    ret.push(escapeHTML(change.value));\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n  return ret.join('');\n}\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };\n", "import { DiffFile, _cacheMap } from \"@git-diff-view/core\";\nimport { createTwoFilesPatch, type PatchOptions } from \"diff\";\n\nimport type { DiffHighlighterLang } from \"@git-diff-view/core\";\n\n_cacheMap.name = \"@git-diff-view/file\";\n\nexport function generateDiffFile(\n  oldFileName: string,\n  oldFileContent: string,\n  newFileName: string,\n  newFileContent: string,\n  oldFileLang: DiffHighlighterLang,\n  newFileLang: DiffHighlighterLang,\n  option?: PatchOptions,\n  uuid?: string\n): DiffFile;\nexport function generateDiffFile(\n  oldFileName: string,\n  oldFileContent: string,\n  newFileName: string,\n  newFileContent: string,\n  oldFileLang: string,\n  newFileLang: string,\n  option?: PatchOptions,\n  uuid?: string\n): DiffFile;\nexport function generateDiffFile(\n  oldFileName: string,\n  oldFileContent: string,\n  newFileName: string,\n  newFileContent: string,\n  oldFileLang: DiffHighlighterLang | string,\n  newFileLang: DiffHighlighterLang | string,\n  option?: PatchOptions,\n  uuid?: string\n) {\n  const diffString = createTwoFilesPatch(oldFileName, newFileName, oldFileContent, newFileContent, \"\", \"\", option);\n\n  return new DiffFile(\n    oldFileName,\n    oldFileContent,\n    newFileName,\n    newFileContent,\n    [diffString],\n    oldFileLang,\n    newFileLang,\n    uuid\n  );\n}\n\nexport * from \"@git-diff-view/core\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,OAAO;AAAC;AACjB,KAAK,YAAY;AAAA,EACf,MAAM,SAAS,KAAK,WAAW,WAAW;AACxC,QAAI;AACJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,QAAQ;AACvB,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU,CAAC;AAAA,IACb;AACA,QAAI,OAAO;AACX,aAAS,KAAK,OAAO;AACnB,cAAQ,KAAK,YAAY,OAAO,OAAO;AACvC,UAAI,UAAU;AACZ,mBAAW,WAAY;AACrB,mBAAS,KAAK;AAAA,QAChB,GAAG,CAAC;AACJ,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAGA,gBAAY,KAAK,UAAU,WAAW,OAAO;AAC7C,gBAAY,KAAK,UAAU,WAAW,OAAO;AAC7C,gBAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AAC9D,gBAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AAC9D,QAAI,SAAS,UAAU,QACrB,SAAS,UAAU;AACrB,QAAI,aAAa;AACjB,QAAI,gBAAgB,SAAS;AAC7B,QAAI,QAAQ,iBAAiB,MAAM;AACjC,sBAAgB,KAAK,IAAI,eAAe,QAAQ,aAAa;AAAA,IAC/D;AACA,QAAI,oBAAoB,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB;AACzH,QAAI,sBAAsB,KAAK,IAAI,IAAI;AACvC,QAAI,WAAW,CAAC;AAAA,MACd,QAAQ;AAAA,MACR,eAAe;AAAA,IACjB,CAAC;AAGD,QAAI,SAAS,KAAK,cAAc,SAAS,CAAC,GAAG,WAAW,WAAW,GAAG,OAAO;AAC7E,QAAI,SAAS,CAAC,EAAE,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAE5D,aAAO,KAAK,YAAY,MAAM,SAAS,CAAC,EAAE,eAAe,WAAW,WAAW,KAAK,eAAe,CAAC;AAAA,IACtG;AAmBA,QAAI,wBAAwB,WAC1B,wBAAwB;AAG1B,aAAS,iBAAiB;AACxB,eAAS,eAAe,KAAK,IAAI,uBAAuB,CAAC,UAAU,GAAG,gBAAgB,KAAK,IAAI,uBAAuB,UAAU,GAAG,gBAAgB,GAAG;AACpJ,YAAI,WAAW;AACf,YAAI,aAAa,SAAS,eAAe,CAAC,GACxC,UAAU,SAAS,eAAe,CAAC;AACrC,YAAI,YAAY;AAEd,mBAAS,eAAe,CAAC,IAAI;AAAA,QAC/B;AACA,YAAI,SAAS;AACb,YAAI,SAAS;AAEX,cAAI,gBAAgB,QAAQ,SAAS;AACrC,mBAAS,WAAW,KAAK,iBAAiB,gBAAgB;AAAA,QAC5D;AACA,YAAI,YAAY,cAAc,WAAW,SAAS,IAAI;AACtD,YAAI,CAAC,UAAU,CAAC,WAAW;AAEzB,mBAAS,YAAY,IAAI;AACzB;AAAA,QACF;AAKA,YAAI,CAAC,aAAa,UAAU,WAAW,SAAS,QAAQ,QAAQ;AAC9D,qBAAW,KAAK,UAAU,SAAS,MAAM,OAAO,GAAG,OAAO;AAAA,QAC5D,OAAO;AACL,qBAAW,KAAK,UAAU,YAAY,OAAO,MAAM,GAAG,OAAO;AAAA,QAC/D;AACA,iBAAS,KAAK,cAAc,UAAU,WAAW,WAAW,cAAc,OAAO;AACjF,YAAI,SAAS,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAEzD,iBAAO,KAAK,YAAY,MAAM,SAAS,eAAe,WAAW,WAAW,KAAK,eAAe,CAAC;AAAA,QACnG,OAAO;AACL,mBAAS,YAAY,IAAI;AACzB,cAAI,SAAS,SAAS,KAAK,QAAQ;AACjC,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AACA,cAAI,SAAS,KAAK,QAAQ;AACxB,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA,MACF;AACA;AAAA,IACF;AAMA,QAAI,UAAU;AACZ,OAAC,SAAS,OAAO;AACf,mBAAW,WAAY;AACrB,cAAI,aAAa,iBAAiB,KAAK,IAAI,IAAI,qBAAqB;AAClE,mBAAO,SAAS;AAAA,UAClB;AACA,cAAI,CAAC,eAAe,GAAG;AACrB,iBAAK;AAAA,UACP;AAAA,QACF,GAAG,CAAC;AAAA,MACN,GAAG;AAAA,IACL,OAAO;AACL,aAAO,cAAc,iBAAiB,KAAK,IAAI,KAAK,qBAAqB;AACvE,YAAI,MAAM,eAAe;AACzB,YAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,SAAS,UAAU,MAAM,OAAO,SAAS,WAAW,SAAS;AACtE,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,CAAC,QAAQ,qBAAqB,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS;AAC1F,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO,KAAK,QAAQ;AAAA,UACpB;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc,UAAU,WAAW,WAAW,cAAc,SAAS;AAC3F,QAAI,SAAS,UAAU,QACrB,SAAS,UAAU,QACnB,SAAS,SAAS,QAClB,SAAS,SAAS,cAClB,cAAc;AAChB,WAAO,SAAS,IAAI,UAAU,SAAS,IAAI,UAAU,KAAK,OAAO,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,GAAG,OAAO,GAAG;AACvH;AACA;AACA;AACA,UAAI,QAAQ,mBAAmB;AAC7B,iBAAS,gBAAgB;AAAA,UACvB,OAAO;AAAA,UACP,mBAAmB,SAAS;AAAA,UAC5B,OAAO;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,mBAAmB;AAC7C,eAAS,gBAAgB;AAAA,QACvB,OAAO;AAAA,QACP,mBAAmB,SAAS;AAAA,QAC5B,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AACA,aAAS,SAAS;AAClB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,MAAM,OAAO,SAAS;AAC5C,QAAI,QAAQ,YAAY;AACtB,aAAO,QAAQ,WAAW,MAAM,KAAK;AAAA,IACvC,OAAO;AACL,aAAO,SAAS,SAAS,QAAQ,cAAc,KAAK,YAAY,MAAM,MAAM,YAAY;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,GAAG;AACZ,YAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,WAAO,MAAM,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,MAAM,SAAS,KAAK,OAAO;AACzB,WAAO,MAAM,KAAK,EAAE;AAAA,EACtB;AAAA,EACA,aAAa,SAAS,YAAY,eAAe;AAC/C,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAYA,OAAM,eAAe,WAAW,WAAW,iBAAiB;AAG/E,MAAI,aAAa,CAAC;AAClB,MAAI;AACJ,SAAO,eAAe;AACpB,eAAW,KAAK,aAAa;AAC7B,oBAAgB,cAAc;AAC9B,WAAO,cAAc;AACrB,oBAAgB;AAAA,EAClB;AACA,aAAW,QAAQ;AACnB,MAAI,eAAe,GACjB,eAAe,WAAW,QAC1B,SAAS,GACT,SAAS;AACX,SAAO,eAAe,cAAc,gBAAgB;AAClD,QAAI,YAAY,WAAW,YAAY;AACvC,QAAI,CAAC,UAAU,SAAS;AACtB,UAAI,CAAC,UAAU,SAAS,iBAAiB;AACvC,YAAI,QAAQ,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK;AAC5D,gBAAQ,MAAM,IAAI,SAAUC,QAAO,GAAG;AACpC,cAAI,WAAW,UAAU,SAAS,CAAC;AACnC,iBAAO,SAAS,SAASA,OAAM,SAAS,WAAWA;AAAA,QACrD,CAAC;AACD,kBAAU,QAAQD,MAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,kBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAAA,MAC/E;AACA,gBAAU,UAAU;AAGpB,UAAI,CAAC,UAAU,OAAO;AACpB,kBAAU,UAAU;AAAA,MACtB;AAAA,IACF,OAAO;AACL,gBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAC7E,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAgB,IAAI,KAAK;AAK7B,SAAS,oBAAoB,MAAM,MAAM;AACvC,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACnD,QAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;AACtB,aAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,GAAG,CAAC;AACxB;AACA,SAAS,oBAAoB,MAAM,MAAM;AACvC,MAAI;AAKJ,MAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,SAAS,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,GAAG;AACpE,WAAO;AAAA,EACT;AACA,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACnD,QAAI,KAAK,KAAK,UAAU,IAAI,EAAE,KAAK,KAAK,KAAK,UAAU,IAAI,EAAE,GAAG;AAC9D,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,CAAC,CAAC;AACtB;AACA,SAAS,cAAc,QAAQ,WAAW,WAAW;AACnD,MAAI,OAAO,MAAM,GAAG,UAAU,MAAM,KAAK,WAAW;AAClD,UAAM,MAAM,UAAU,OAAO,KAAK,UAAU,MAAM,GAAG,6BAA6B,EAAE,OAAO,KAAK,UAAU,SAAS,GAAG,iBAAiB,CAAC;AAAA,EAC1I;AACA,SAAO,YAAY,OAAO,MAAM,UAAU,MAAM;AAClD;AACA,SAAS,cAAc,QAAQ,WAAW,WAAW;AACnD,MAAI,CAAC,WAAW;AACd,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO,MAAM,CAAC,UAAU,MAAM,KAAK,WAAW;AAChD,UAAM,MAAM,UAAU,OAAO,KAAK,UAAU,MAAM,GAAG,2BAA2B,EAAE,OAAO,KAAK,UAAU,SAAS,GAAG,iBAAiB,CAAC;AAAA,EACxI;AACA,SAAO,OAAO,MAAM,GAAG,CAAC,UAAU,MAAM,IAAI;AAC9C;AACA,SAAS,aAAa,QAAQ,WAAW;AACvC,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC5C;AACA,SAAS,aAAa,QAAQ,WAAW;AACvC,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC5C;AACA,SAAS,eAAe,SAAS,SAAS;AACxC,SAAO,QAAQ,MAAM,GAAG,aAAa,SAAS,OAAO,CAAC;AACxD;AAGA,SAAS,aAAa,GAAG,GAAG;AAE1B,MAAI,SAAS;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACvB,aAAS,EAAE,SAAS,EAAE;AAAA,EACxB;AACA,MAAI,OAAO,EAAE;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACvB,WAAO,EAAE;AAAA,EACX;AAIA,MAAI,MAAM,MAAM,IAAI;AACpB,MAAI,IAAI;AACR,MAAI,CAAC,IAAI;AACT,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB,OAAO;AACL,UAAI,CAAC,IAAI;AAAA,IACX;AACA,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC5B,UAAI,IAAI,CAAC;AAAA,IACX;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,IAAI,QAAQ,IAAI,EAAE,QAAQ,KAAK;AACtC,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC5B,UAAI,IAAI,CAAC;AAAA,IACX;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAkCA,IAAI,oBAAoB;AA2BxB,IAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,GAAG,GAAG,IAAI;AAC7H,IAAI,WAAW,IAAI,KAAK;AACxB,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAChD,MAAI,QAAQ,YAAY;AACtB,WAAO,KAAK,YAAY;AACxB,YAAQ,MAAM,YAAY;AAAA,EAC5B;AACA,SAAO,KAAK,KAAK,MAAM,MAAM,KAAK;AACpC;AACA,SAAS,WAAW,SAAU,OAAO;AACnC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI;AACJ,MAAI,QAAQ,eAAe;AACzB,QAAI,QAAQ,cAAc,gBAAgB,EAAE,eAAe,QAAQ;AACjE,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,YAAQ,MAAM,KAAK,QAAQ,cAAc,QAAQ,KAAK,GAAG,SAAU,SAAS;AAC1E,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,OAAO;AACL,YAAQ,MAAM,MAAM,2BAA2B,KAAK,CAAC;AAAA,EACvD;AACA,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,QAAM,QAAQ,SAAU,MAAM;AAC5B,QAAI,KAAK,KAAK,IAAI,GAAG;AACnB,UAAI,YAAY,MAAM;AACpB,eAAO,KAAK,IAAI;AAAA,MAClB,OAAO;AACL,eAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,MACjC;AAAA,IACF,WAAW,KAAK,KAAK,QAAQ,GAAG;AAC9B,UAAI,OAAO,OAAO,SAAS,CAAC,KAAK,UAAU;AACzC,eAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,MACjC,OAAO;AACL,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,eAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AACA,SAAS,OAAO,SAAU,QAAQ;AAMhC,SAAO,OAAO,IAAI,SAAU,OAAO,GAAG;AACpC,QAAI,KAAK,GAAG;AACV,aAAO;AAAA,IACT,OAAO;AACL,aAAO,MAAM,QAAQ,QAAQ,EAAE;AAAA,IACjC;AAAA,EACF,CAAC,EAAE,KAAK,EAAE;AACZ;AACA,SAAS,cAAc,SAAU,SAAS,SAAS;AACjD,MAAI,CAAC,WAAW,QAAQ,mBAAmB;AACzC,WAAO;AAAA,EACT;AACA,MAAI,WAAW;AAGf,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,UAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,OAAO,OAAO;AAChB,kBAAY;AAAA,IACd,WAAW,OAAO,SAAS;AACzB,iBAAW;AAAA,IACb,OAAO;AACL,UAAI,aAAa,UAAU;AAEzB,wCAAgC,UAAU,UAAU,WAAW,MAAM;AAAA,MACvE;AACA,iBAAW;AACX,kBAAY;AACZ,iBAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,aAAa,UAAU;AACzB,oCAAgC,UAAU,UAAU,WAAW,IAAI;AAAA,EACrE;AACA,SAAO;AACT;AAWA,SAAS,gCAAgC,WAAW,UAAU,WAAW,SAAS;AA2ChF,MAAI,YAAY,WAAW;AACzB,QAAI,cAAc,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAChD,QAAI,cAAc,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAChD,QAAI,cAAc,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACjD,QAAI,cAAc,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACjD,QAAI,WAAW;AACb,UAAI,iBAAiB,oBAAoB,aAAa,WAAW;AACjE,gBAAU,QAAQ,cAAc,UAAU,OAAO,aAAa,cAAc;AAC5E,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAChE;AACA,QAAI,SAAS;AACX,UAAI,iBAAiB,oBAAoB,aAAa,WAAW;AACjE,cAAQ,QAAQ,cAAc,QAAQ,OAAO,aAAa,cAAc;AACxE,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAChE;AAAA,EACF,WAAW,WAAW;AAOpB,QAAI,WAAW;AACb,gBAAU,QAAQ,UAAU,MAAM,QAAQ,QAAQ,EAAE;AAAA,IACtD;AACA,QAAI,SAAS;AACX,cAAQ,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,EAAE;AAAA,IAClD;AAAA,EAEF,WAAW,aAAa,SAAS;AAC/B,QAAI,YAAY,QAAQ,MAAM,MAAM,MAAM,EAAE,CAAC,GAC3C,aAAa,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC,GAC3C,WAAW,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAI3C,QAAI,aAAa,oBAAoB,WAAW,UAAU;AAC1D,aAAS,QAAQ,aAAa,SAAS,OAAO,UAAU;AAKxD,QAAI,WAAW,oBAAoB,aAAa,WAAW,UAAU,GAAG,QAAQ;AAChF,aAAS,QAAQ,aAAa,SAAS,OAAO,QAAQ;AACtD,YAAQ,QAAQ,cAAc,QAAQ,OAAO,WAAW,QAAQ;AAIhE,cAAU,QAAQ,cAAc,UAAU,OAAO,WAAW,UAAU,MAAM,GAAG,UAAU,SAAS,SAAS,MAAM,CAAC;AAAA,EACpH,WAAW,SAAS;AAIlB,QAAI,kBAAkB,QAAQ,MAAM,MAAM,MAAM,EAAE,CAAC;AACnD,QAAI,mBAAmB,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AACrD,QAAI,UAAU,eAAe,kBAAkB,eAAe;AAC9D,aAAS,QAAQ,aAAa,SAAS,OAAO,OAAO;AAAA,EACvD,WAAW,WAAW;AAIpB,QAAI,oBAAoB,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACvD,QAAI,mBAAmB,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AACrD,QAAI,WAAW,eAAe,mBAAmB,gBAAgB;AACjE,aAAS,QAAQ,aAAa,SAAS,OAAO,QAAQ;AAAA,EACxD;AACF;AACA,IAAI,oBAAoB,IAAI,KAAK;AACjC,kBAAkB,WAAW,SAAU,OAAO;AAM5C,MAAI,QAAQ,IAAI,OAAO,cAAc,OAAO,mBAAmB,qBAAqB,EAAE,OAAO,mBAAmB,GAAG,GAAG,IAAI;AAC1H,SAAO,MAAM,MAAM,KAAK,KAAK,CAAC;AAChC;AAmBA,IAAI,WAAW,IAAI,KAAK;AACxB,SAAS,WAAW,SAAU,OAAO,SAAS;AAC5C,MAAI,QAAQ,iBAAiB;AAE3B,YAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,EACrC;AACA,MAAI,WAAW,CAAC,GACd,mBAAmB,MAAM,MAAM,WAAW;AAG5C,MAAI,CAAC,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAClD,qBAAiB,IAAI;AAAA,EACvB;AAGA,WAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,QAAI,OAAO,iBAAiB,CAAC;AAC7B,QAAI,IAAI,KAAK,CAAC,QAAQ,gBAAgB;AACpC,eAAS,SAAS,SAAS,CAAC,KAAK;AAAA,IACnC,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAQhD,MAAI,QAAQ,kBAAkB;AAC5B,QAAI,CAAC,QAAQ,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,QAAI,CAAC,QAAQ,kBAAkB,CAAC,MAAM,SAAS,IAAI,GAAG;AACpD,cAAQ,MAAM,KAAK;AAAA,IACrB;AAAA,EACF,WAAW,QAAQ,sBAAsB,CAAC,QAAQ,gBAAgB;AAChE,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,aAAO,KAAK,MAAM,GAAG,EAAE;AAAA,IACzB;AACA,QAAI,MAAM,SAAS,IAAI,GAAG;AACxB,cAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,KAAK,UAAU,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC9D;AACA,SAAS,UAAU,QAAQ,QAAQ,UAAU;AAC3C,SAAO,SAAS,KAAK,QAAQ,QAAQ,QAAQ;AAC/C;AAeA,IAAI,eAAe,IAAI,KAAK;AAC5B,aAAa,WAAW,SAAU,OAAO;AACvC,SAAO,MAAM,MAAM,uBAAuB;AAC5C;AAKA,IAAI,UAAU,IAAI,KAAK;AACvB,QAAQ,WAAW,SAAU,OAAO;AAClC,SAAO,MAAM,MAAM,eAAe;AACpC;AAKA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUE,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AACvC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,OAAO,EAAG,QAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AACA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AACA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAC/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,IAAI,WAAW,IAAI,KAAK;AAGxB,SAAS,kBAAkB;AAC3B,SAAS,WAAW,SAAS;AAC7B,SAAS,YAAY,SAAU,OAAO,SAAS;AAC7C,MAAI,uBAAuB,QAAQ,sBACjC,wBAAwB,QAAQ,mBAChC,oBAAoB,0BAA0B,SAAS,SAAU,GAAG,GAAG;AACrE,WAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,EAC3D,IAAI;AACN,SAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,aAAa,OAAO,MAAM,MAAM,iBAAiB,GAAG,mBAAmB,IAAI;AACvI;AACA,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAChD,SAAO,KAAK,UAAU,OAAO,KAAK,UAAU,KAAK,QAAQ,cAAc,IAAI,GAAG,MAAM,QAAQ,cAAc,IAAI,GAAG,OAAO;AAC1H;AAOA,SAAS,aAAa,KAAK,OAAO,kBAAkB,UAAU,KAAK;AACjE,UAAQ,SAAS,CAAC;AAClB,qBAAmB,oBAAoB,CAAC;AACxC,MAAI,UAAU;AACZ,UAAM,SAAS,KAAK,GAAG;AAAA,EACzB;AACA,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB,aAAO,iBAAiB,CAAC;AAAA,IAC3B;AAAA,EACF;AACA,MAAI;AACJ,MAAI,qBAAqB,OAAO,UAAU,SAAS,KAAK,GAAG,GAAG;AAC5D,UAAM,KAAK,GAAG;AACd,uBAAmB,IAAI,MAAM,IAAI,MAAM;AACvC,qBAAiB,KAAK,gBAAgB;AACtC,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAClC,uBAAiB,CAAC,IAAI,aAAa,IAAI,CAAC,GAAG,OAAO,kBAAkB,UAAU,GAAG;AAAA,IACnF;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,IAAI,QAAQ;AACrB,UAAM,IAAI,OAAO;AAAA,EACnB;AACA,MAAI,QAAQ,GAAG,MAAM,YAAY,QAAQ,MAAM;AAC7C,UAAM,KAAK,GAAG;AACd,uBAAmB,CAAC;AACpB,qBAAiB,KAAK,gBAAgB;AACtC,QAAI,aAAa,CAAC,GAChB;AACF,SAAK,QAAQ,KAAK;AAEhB,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,GAAG;AACnD,mBAAW,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AACA,eAAW,KAAK;AAChB,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,aAAO,WAAW,CAAC;AACnB,uBAAiB,IAAI,IAAI,aAAa,IAAI,IAAI,GAAG,OAAO,kBAAkB,UAAU,IAAI;AAAA,IAC1F;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AAAA,EACvB,OAAO;AACL,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAI,YAAY,IAAI,KAAK;AACzB,UAAU,WAAW,SAAU,OAAO;AACpC,SAAO,MAAM,MAAM;AACrB;AACA,UAAU,OAAO,UAAU,cAAc,SAAU,OAAO;AACxD,SAAO;AACT;AAieA,SAAS,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AAChG,MAAI,CAAC,SAAS;AACZ,cAAU,CAAC;AAAA,EACb;AACA,MAAI,OAAO,YAAY,YAAY;AACjC,cAAU;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,YAAY,aAAa;AAC1C,YAAQ,UAAU;AAAA,EACpB;AACA,MAAI,QAAQ,gBAAgB;AAC1B,UAAM,IAAI,MAAM,6FAA6F;AAAA,EAC/G;AACA,MAAI,CAAC,QAAQ,UAAU;AACrB,WAAO,uBAAuB,UAAU,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAClE,OAAO;AACL,QAAI,WAAW,SACb,YAAY,SAAS;AACvB,cAAU,QAAQ,QAAQ,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,MACxE,UAAU,SAAS,SAASC,OAAM;AAChC,YAAI,QAAQ,uBAAuBA,KAAI;AACvC,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACA,WAAS,uBAAuBA,OAAM;AAIpC,QAAI,CAACA,OAAM;AACT;AAAA,IACF;AACA,IAAAA,MAAK,KAAK;AAAA,MACR,OAAO;AAAA,MACP,OAAO,CAAC;AAAA,IACV,CAAC;AAED,aAAS,aAAa,OAAO;AAC3B,aAAO,MAAM,IAAI,SAAU,OAAO;AAChC,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB,GAClB,gBAAgB,GAChB,WAAW,CAAC,GACZ,UAAU,GACV,UAAU;AACZ,QAAI,QAAQ,SAASC,SAAQ;AAC3B,UAAI,UAAUD,MAAK,CAAC,GAClB,QAAQ,QAAQ,SAAS,WAAW,QAAQ,KAAK;AACnD,cAAQ,QAAQ;AAChB,UAAI,QAAQ,SAAS,QAAQ,SAAS;AACpC,YAAI;AAEJ,YAAI,CAAC,eAAe;AAClB,cAAI,OAAOA,MAAK,IAAI,CAAC;AACrB,0BAAgB;AAChB,0BAAgB;AAChB,cAAI,MAAM;AACR,uBAAW,QAAQ,UAAU,IAAI,aAAa,KAAK,MAAM,MAAM,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC;AACrF,6BAAiB,SAAS;AAC1B,6BAAiB,SAAS;AAAA,UAC5B;AAAA,QACF;AAGA,SAAC,YAAY,UAAU,KAAK,MAAM,WAAW,mBAAmB,MAAM,IAAI,SAAU,OAAO;AACzF,kBAAQ,QAAQ,QAAQ,MAAM,OAAO;AAAA,QACvC,CAAC,CAAC,CAAC;AAGH,YAAI,QAAQ,OAAO;AACjB,qBAAW,MAAM;AAAA,QACnB,OAAO;AACL,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,OAAO;AAEL,YAAI,eAAe;AAEjB,cAAI,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAIA,MAAK,SAAS,GAAG;AAC9D,gBAAI;AAEJ,aAAC,aAAa,UAAU,KAAK,MAAM,YAAY,mBAAmB,aAAa,KAAK,CAAC,CAAC;AAAA,UACxF,OAAO;AACL,gBAAI;AAEJ,gBAAI,cAAc,KAAK,IAAI,MAAM,QAAQ,QAAQ,OAAO;AACxD,aAAC,aAAa,UAAU,KAAK,MAAM,YAAY,mBAAmB,aAAa,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AAC5G,gBAAI,QAAQ;AAAA,cACV,UAAU;AAAA,cACV,UAAU,UAAU,gBAAgB;AAAA,cACpC,UAAU;AAAA,cACV,UAAU,UAAU,gBAAgB;AAAA,cACpC,OAAO;AAAA,YACT;AACA,kBAAM,KAAK,KAAK;AAChB,4BAAgB;AAChB,4BAAgB;AAChB,uBAAW,CAAC;AAAA,UACd;AAAA,QACF;AACA,mBAAW,MAAM;AACjB,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,KAAK;AACpC,YAAM;AAAA,IACR;AAIA,aAAS,KAAK,GAAG,SAAS,OAAO,KAAK,OAAO,QAAQ,MAAM;AACzD,UAAI,OAAO,OAAO,EAAE;AACpB,eAAS,MAAM,GAAG,MAAM,KAAK,MAAM,QAAQ,OAAO;AAChD,YAAI,KAAK,MAAM,GAAG,EAAE,SAAS,IAAI,GAAG;AAClC,eAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE;AAAA,QAC/C,OAAO;AACL,eAAK,MAAM,OAAO,MAAM,GAAG,GAAG,8BAA8B;AAC5D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,YAAYA,OAAM;AACzB,MAAI,MAAM,QAAQA,KAAI,GAAG;AACvB,WAAOA,MAAK,IAAI,WAAW,EAAE,KAAK,IAAI;AAAA,EACxC;AACA,MAAI,MAAM,CAAC;AACX,MAAIA,MAAK,eAAeA,MAAK,aAAa;AACxC,QAAI,KAAK,YAAYA,MAAK,WAAW;AAAA,EACvC;AACA,MAAI,KAAK,qEAAqE;AAC9E,MAAI,KAAK,SAASA,MAAK,eAAe,OAAOA,MAAK,cAAc,cAAc,KAAK,MAAOA,MAAK,UAAU;AACzG,MAAI,KAAK,SAASA,MAAK,eAAe,OAAOA,MAAK,cAAc,cAAc,KAAK,MAAOA,MAAK,UAAU;AACzG,WAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AAC1C,QAAI,OAAOA,MAAK,MAAM,CAAC;AAIvB,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,KAAK,SAAS,KAAK,WAAW,MAAM,KAAK,WAAW,OAAO,KAAK,WAAW,MAAM,KAAK,WAAW,KAAK;AAC1G,QAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAChC;AACA,SAAO,IAAI,KAAK,IAAI,IAAI;AAC1B;AACA,SAAS,oBAAoB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AACpG,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY;AACjC,cAAU;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,GAAG,YAAY,aAAa,QAAQ,cAAc,UAAU,UAAU,WAAW;AACnF,QAAI,WAAW,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,OAAO;AACtG,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,WAAO,YAAY,QAAQ;AAAA,EAC7B,OAAO;AACL,QAAI,YAAY,SACd,aAAa,UAAU;AACzB,oBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,MAC9H,UAAU,SAAS,SAASE,WAAU;AACpC,YAAI,CAACA,WAAU;AACb,qBAAW;AAAA,QACb,OAAO;AACL,qBAAW,YAAYA,SAAQ,CAAC;AAAA,QAClC;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AAQA,SAAS,WAAW,MAAM;AACxB,MAAI,gBAAgB,KAAK,SAAS,IAAI;AACtC,MAAI,SAAS,KAAK,MAAM,IAAI,EAAE,IAAI,SAAU,MAAM;AAChD,WAAO,OAAO;AAAA,EAChB,CAAC;AACD,MAAI,eAAe;AACjB,WAAO,IAAI;AAAA,EACb,OAAO;AACL,WAAO,KAAK,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,EACvC;AACA,SAAO;AACT;;;ACvjDA,UAAU,OAAO;SAsBD,iBACd,aACA,gBACA,aACA,gBACA,aACA,aACA,QACA,MAAa;AAEb,QAAM,aAAa,oBAAoB,aAAa,aAAa,gBAAgB,gBAAgB,IAAI,IAAI,MAAM;AAE/G,SAAO,IAAI,SACT,aACA,gBACA,aACA,gBACA,CAAC,UAAU,GACX,aACA,aACA,IAAI;AAER;", "names": ["diff", "value", "r", "o", "diff", "_loop", "patchObj"]}